name: tests

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

jobs:

  unit:
    runs-on: ubuntu-latest
    steps:
    
    - name: Check out code
      uses: actions/checkout@v3

    - name: Unit Tests
      run: go test -v ./...

  coverage:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup go
        uses: actions/setup-go@v2
        with:
          go-version: '1.20'
      - name: Setup
        run: |
          go install github.com/mattn/goveralls@latest
      - name: Test
        run: |
          go test -v ./... -parallel=10 -covermode=count -coverprofile=coverage.out
      - name: Send coverage
        env:
          COVERALLS_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          goveralls -coverprofile=coverage.out -service=github