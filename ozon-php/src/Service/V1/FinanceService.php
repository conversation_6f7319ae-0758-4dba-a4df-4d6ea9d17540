<?php

declare(strict_types=1);

namespace Gam6itko\OzonSeller\Service\V1;

use Gam6itko\OzonSeller\Service\AbstractService;
use Gam6itko\OzonSeller\Utils\ArrayHelper;

class FinanceService extends AbstractService
{
    /**
     * Returns a list of reports which were previously generated by <PERSON>ller.
     *
     * @see https://docs.ozon.ru/api/seller/#tag/FinanceAPI
     *
     * @return array|string
     */
    public function realization(array $query)
    {
        $query = ArrayHelper::pick($query, ['date']);

        return $this->request('POST', '/v1/finance/realization', $query);
    }
}
