package main

import (
	"fmt"
	"github.com/gogf/gf/v2/util/gconv"
	"lens/internal/api/ozon"
	"strings"
	"testing"

	"github.com/xuri/excelize/v2"
)

func TestGetClusterAndWarehouseList(t *testing.T) {
	// 创建 Ozon API 客户端
	// 注意：这里需要替换为实际的 API Key 和 Client ID
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")

	// 创建请求参数
	req := &ozonapi.ClusterListRequest{
		ClusterType: "CLUSTER_TYPE_OZON", // 俄罗斯集群 或 "CLUSTER_TYPE_CIS" 独联体集群
	}

	// 调用API
	resp, err := client.FBO.GetClusterAndWarehouseList(req)
	if err != nil {
		t.Fatalf("获取集群和仓库列表失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印结果
	fmt.Println("\n=== 集群和仓库列表 ===")
	fmt.Printf("集群数量: %d\n", len(resp.Clusters))

	// 打印集群详细信息
	for i, cluster := range resp.Clusters {
		fmt.Printf("\n集群 #%d:\n", i+1)
		fmt.Printf("- 集群ID: %d\n", cluster.Id)
		fmt.Printf("- 集群名称: %s\n", cluster.Name)
		fmt.Printf("- 仓库数量: %d\n", len(cluster.LogisticClusters))
		if cluster.Id == 154 {
			// 打印仓库信息
			for j, logisticCluster := range cluster.LogisticClusters {
				fmt.Printf("  物流集群 #%d:\n", j+1)
				fmt.Printf("  - 仓库数量: %d\n", len(logisticCluster.Warehouses))
				for k, warehouse := range logisticCluster.Warehouses {
					fmt.Printf("    仓库 #%d:\n", k+1)
					fmt.Printf("    - 仓库名称: %s\n", warehouse.Name)
					fmt.Printf("    - 仓库ID: %d\n", warehouse.WarehouseId)
					fmt.Printf("    - 类型: %s\n", warehouse.Type)
				}
			}
		}
	}
}

func TestSearchFboWarehouses(t *testing.T) {
	// 创建 Ozon API 客户端
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")

	// 创建请求参数
	req := &ozonapi.WarehouseFboListRequest{
		FilterBySupplyType: []string{"CREATE_TYPE_CROSSDOCK"}, // 或 "CREATE_TYPE_DIRECT"
		Search:             "Москва",                          // 搜索莫斯科的仓库
	}

	// 调用API
	resp, err := client.FBO.SearchFboWarehouses(req)
	if err != nil {
		t.Fatalf("搜索FBO仓库失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印结果
	fmt.Println("\n=== FBO仓库搜索结果 ===")
	fmt.Printf("找到仓库数量: %d\n", len(resp.Search))

	// 打印仓库详细信息
	for i, warehouse := range resp.Search {
		fmt.Printf("\n仓库 #%d:\n", i+1)
		fmt.Printf("- 仓库名称: %s\n", warehouse.Name)
		fmt.Printf("- 仓库ID: %d\n", warehouse.WarehouseID)
		fmt.Printf("- 仓库类型: %s\n", warehouse.WarehouseType)
		fmt.Printf("- 地址: %s\n", warehouse.Address)

		// 打印坐标信息
		fmt.Printf("- 纬度: %f\n", warehouse.Coordinates.Latitude)
		fmt.Printf("- 经度: %f\n", warehouse.Coordinates.Longitude)
	}
}

// TestCreateSupplyDraftFromCluster 测试根据集群列表创建供应草稿
func TestCreateSupplyDraftFromCluster(t *testing.T) {
	// 创建 Ozon API 客户端
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")

	// 1. 读取Excel表格数据
	fmt.Println("正在读取Excel表格数据...")
	f, err := excelize.OpenFile("20250726库存情况和分配计划-对外.xlsx")
	if err != nil {
		t.Fatalf("打开Excel文件失败: %v", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			t.Fatalf("关闭Excel文件失败: %v", err)
		}
	}()

	// 获取第一个工作表的名称
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		t.Fatal("无法获取Excel工作表名称")
	}

	// 解析每个集群下的SKU数量和箱数
	type ClusterProductInfo struct {
		ClusterName string
		SKU         string
		Quantity    int
		Boxes       float64
	}

	clusterProductsMap := make(map[string][]ClusterProductInfo)
	clusters := []string{}
	products := []string{}

	fmt.Printf("读取工作表: %s\n", sheetName)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		t.Fatalf("读取行数据失败: %v", err)
	}

	// 读取集群名称和产品SKU
	for i, row := range rows {
		if i == 0 { // 第一行是集群名称
			for j := 10; j < 52 && j < len(row); j += 2 {
				if row[j] != "" {
					clusters = append(clusters, strings.Split(row[j], "\n")[0])
				}
			}
		}
		if i >= 3 && len(row) > 0 { // 从第4行开始是产品数据
			if row[0] != "" {
				products = append(products, row[0]) // SKU名称在第1列
			}
		}
	}

	// 解析每个集群下的产品数据
	for i, product := range products {
		rowIndex := i + 3 // 数据从第4行开始
		if rowIndex >= len(rows) {
			break
		}

		row := rows[rowIndex]
		for j, cluster := range clusters {
			colIndex := 10 + j*2 // 数据从第11列开始，每两个列为一个集群的数据
			if colIndex < len(row) {
				quantity := 0
				boxes := 0.0
				// 数量在当前列
				if colIndex < len(row) && row[colIndex] != "" {
					quantity = gconv.Int(row[colIndex])
				}
				// 箱数在下一列
				if colIndex+1 < len(row) && row[colIndex+1] != "" {
					boxes = gconv.Float64(strings.TrimSpace(row[colIndex+1]))
				}
				cpi := ClusterProductInfo{
					ClusterName: cluster,
					SKU:         product,
					Quantity:    quantity,
					Boxes:       boxes,
				}
				clusterProductsMap[cluster] = append(clusterProductsMap[cluster], cpi)
			}
		}
	}

	fmt.Printf("从Excel文件中读取到 %d 个集群商品信息\n", len(clusterProductsMap))

	// 打印每个集群的信息
	for clusterName, products := range clusterProductsMap {
		fmt.Printf("\n集群 %s 的商品信息:\n", clusterName)
		totalQuantity := 0
		totalBoxes := 0.0
		for _, product := range products {
			fmt.Printf("  - SKU: %s, 数量: %d, 箱数: %.1f \n", product.SKU, product.Quantity, product.Boxes)
			totalQuantity += product.Quantity
			totalBoxes += product.Boxes
		}
		fmt.Printf("  集群总计: 数量 %d, 箱数 %.1f \n", totalQuantity, totalBoxes)
	}

	// 2. 获取集群和仓库列表
	clusterReq := &ozonapi.ClusterListRequest{
		ClusterType: "CLUSTER_TYPE_OZON", // 俄罗斯集群
	}

	clusterResp, err := client.FBO.GetClusterAndWarehouseList(clusterReq)
	if err != nil {
		t.Fatalf("获取集群和仓库列表失败: %v", err)
	}

	// 验证响应
	if clusterResp == nil {
		t.Fatal("集群和仓库列表响应为空")
	}

	fmt.Printf("\n获取到 %d 个集群\n", len(clusterResp.Clusters))

	// 打印集群信息
	for i, cluster := range clusterResp.Clusters {
		fmt.Printf("\n集群 #%d:\n", i+1)
		fmt.Printf("- 集群ID: %d\n", cluster.Id)
		fmt.Printf("- 集群名称: %s\n", cluster.Name)
		fmt.Printf("- 集群类型: %s\n", cluster.Type)

		// 打印物流集群中的仓库信息
		for j, logisticCluster := range cluster.LogisticClusters {
			fmt.Printf("  物流集群 #%d:\n", j+1)
			for k, warehouse := range logisticCluster.Warehouses {
				fmt.Printf("    仓库 #%d:\n", k+1)
				fmt.Printf("    - 仓库名称: %s\n", warehouse.Name)
				fmt.Printf("    - 仓库ID: %d\n", warehouse.WarehouseId)
				fmt.Printf("    - 类型: %s\n", warehouse.Type)
			}
		}
	}

	// 3. 从集群列表中选择第一个集群创建供应草稿
	if len(clusterResp.Clusters) == 0 {
		t.Fatal("没有获取到任何集群信息")
	}

	for _, cluster := range clusterResp.Clusters {
		cpms := clusterProductsMap[cluster.Name]
		// 准备草稿中的商品项
		var draftItems []struct {
			Quantity int    `json:"quantity"`
			SKU      string `json:"sku"`
		}
		for _, cpm := range cpms {
			if cpm.Quantity == 0{
				continue
			}
			draftItems = append(draftItems, struct {
				Quantity int    `json:"quantity"`
				SKU      string `json:"sku"`
			}{Quantity: cpm.Quantity, SKU: cpm.SKU})
		}

		// 创建供应草稿请求
		draftReq := &ozonapi.DraftCreateRequest{
			ClusterIDs:              []int64{gconv.Int64(cluster.Id)},
			DropOffPointWarehouseID: 0,
			Items:                   draftItems,
			Type:                    "CREATE_TYPE_CROSSDOCK", // 或者 "CREATE_TYPE_DIRECT"
		}

		// 4. 创建供应草稿
		fmt.Printf("\n正在创建供应草稿...\n")
		fmt.Printf("- 集群ID: %d\n", firstClusterID)
		fmt.Printf("- 配送点仓库ID: %d\n", dropOffPointWarehouseID)
		fmt.Printf("- 商品项数量: %d\n", len(draftReq.Items))

		for i, item := range draftReq.Items {
			fmt.Printf("  商品 #%d: SKU=%s, 数量=%d\n", i+1, item.SKU, item.Quantity)
		}

		operationID, err := client.FBO.CreateSupplyDraft(draftReq)
		if err != nil {
			t.Fatalf("创建供应草稿失败: %v", err)
		}

		fmt.Printf("成功创建供应草稿，操作ID: %s\n", operationID)
	}

	// 5. 获取草稿信息
	fmt.Printf("\n正在获取草稿信息...\n")
	draftInfo, err := client.FBO.GetSupplyDraftInfo(operationID)
	if err != nil {
		t.Fatalf("获取供应草稿信息失败: %v", err)
	}

	fmt.Printf("草稿ID: %d, 状态: %s\n", draftInfo.DraftID, draftInfo.Status)

	// 打印草稿中的集群信息
	for i, cluster := range draftInfo.Clusters {
		fmt.Printf("\n草稿集群 #%d:\n", i+1)
		fmt.Printf("- 集群ID: %d\n", cluster.ClusterID)
		fmt.Printf("- 集群名称: %s\n", cluster.ClusterName)

		// 打印仓库信息
		for j, warehouse := range cluster.Warehouses {
			fmt.Printf("  仓库 #%d:\n", j+1)
			fmt.Printf("  - 仓库ID: %d\n", warehouse.SupplyWarehouse.WarehouseID)
			fmt.Printf("  - 仓库名称: %s\n", warehouse.SupplyWarehouse.Name)
		}
	}

	// 检查是否有错误信息
	if len(draftInfo.Errors) > 0 {
		fmt.Printf("\n草稿创建过程中出现 %d 个错误:\n", len(draftInfo.Errors))
		for i, errMsg := range draftInfo.Errors {
			fmt.Printf("错误 #%d: %v\n", i+1, errMsg)
		}
	} else {
		fmt.Printf("\n草稿创建成功，无错误信息。\n")
	}
}
