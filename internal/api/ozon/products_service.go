package ozonapi

import (
	"context"
	"fmt"
	"time"
)

// ProductsService 商品服务，参考ozon-client的Products结构
type ProductsService struct {
	*BaseService
}

// NewProductsService 创建商品服务
func NewProductsService(baseService *BaseService) *ProductsService {
	return &ProductsService{
		BaseService: baseService,
	}
}

// GetStocksInfoParams 获取库存信息参数
type GetStocksInfoParams struct {
	// 下一个数据样本的游标
	Cursor string `json:"cursor"`
	// 回复中条目数量的限制。默认值为1000。最大值为1000
	Limit int32 `json:"limit"`
	// 按产品过滤
	Filter GetStocksInfoFilter `json:"filter"`
}

// GetStocksInfoFilter 库存信息过滤器
type GetStocksInfoFilter struct {
	// 按offer_id参数过滤。可以传递值列表
	OfferID []string `json:"offer_id,omitempty"`
	// 按product_id参数过滤。可以传递值列表
	ProductID []int64 `json:"product_id,omitempty"`
	// 按产品可见性过滤
	Visibility string `json:"visibility,omitempty"`
	// "经济"关税的产品
	WithQuant GetStocksInfoFilterWithQuant `json:"with_quant"`
}

// GetStocksInfoFilterWithQuant 经济产品过滤器
type GetStocksInfoFilterWithQuant struct {
	// 活跃的经济产品
	Created bool `json:"created"`
	// 所有状态的经济产品
	Exists bool `json:"exists"`
}

// GetStocksInfoResponse 获取库存信息响应
type GetStocksInfoResponse struct {
	CommonResponse
	// 下一个数据样本的游标
	Cursor string `json:"cursor"`
	// 显示库存信息的唯一产品数量
	Total int32 `json:"total"`
	// 产品详情
	Items []GetStocksInfoResultItem `json:"items"`
}

// GetStocksInfoResultItem 库存信息结果项
type GetStocksInfoResultItem struct {
	// 卖家系统中的产品标识符
	OfferID string `json:"offer_id"`
	// 产品标识符
	ProductID int64 `json:"product_id"`
	// 库存详情
	Stocks []GetStocksInfoResultItemStock `json:"stocks"`
}

// GetStocksInfoResultItemStock 库存详情
type GetStocksInfoResultItemStock struct {
	// 在仓库中
	Present int32 `json:"present"`
	// 已预留
	Reserved int32 `json:"reserved"`
	// 仓库类型
	Type string `json:"type"`
	// 包装类型
	ShipmentType string `json:"shipment_type"`
	// Ozon系统中的产品标识符，SKU
	SKU int64 `json:"sku"`
}

// GetStocksInfo 返回有关库存中产品数量的信息：
// * 有多少件可用，
// * 有多少被客户预留。
func (s *ProductsService) GetStocksInfo(ctx context.Context, params *GetStocksInfoParams) (*GetStocksInfoResponse, error) {
	result, err := s.Request("POST", "/v4/product/info/stocks", params)
	if err != nil {
		return nil, err
	}

	response := &GetStocksInfoResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// GetProductListParams 获取产品列表参数
type GetProductListParams struct {
	// 过滤器
	Filter GetProductListFilter `json:"filter"`
	// 最后一个ID
	LastID string `json:"last_id,omitempty"`
	// 限制数量
	Limit int32 `json:"limit"`
}

// GetProductListFilter 产品列表过滤器
type GetProductListFilter struct {
	// 按offer_id过滤
	OfferID []string `json:"offer_id,omitempty"`
	// 按product_id过滤
	ProductID []int64 `json:"product_id,omitempty"`
	// 按可见性过滤
	Visibility Visibility `json:"visibility,omitempty"`
}

// GetProductListResponse 获取产品列表响应
type GetProductListResponse struct {
	CommonResponse
	// 产品列表
	Result GetProductListResult `json:"result"`
}

// GetProductListResult 产品列表结果
type GetProductListResult struct {
	// 产品列表
	Items []ProductListItem `json:"items"`
	// 总数
	Total int32 `json:"total"`
	// 最后一个ID
	LastID string `json:"last_id"`
}

// ProductListItem 产品列表项
type ProductListItem struct {
	// 产品ID
	ProductID int64 `json:"product_id"`
	// 卖家商品ID
	OfferID string `json:"offer_id"`
	// 是否归档
	IsArchived bool `json:"is_archived"`
	// 是否自动归档
	IsAutoarchived bool `json:"is_autoarchived"`
}

// GetProductList 获取产品列表
func (s *ProductsService) GetProductList(ctx context.Context, params *GetProductListParams) (*GetProductListResponse, error) {
	result, err := s.Request("POST", "/v2/product/list", params)
	if err != nil {
		return nil, err
	}

	response := &GetProductListResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// GetProductInfoParams 获取产品信息参数
type GetProductInfoParams struct {
	// 产品ID
	ProductID int64 `json:"product_id,omitempty"`
	// SKU
	SKU int64 `json:"sku,omitempty"`
	// 卖家商品ID
	OfferID string `json:"offer_id,omitempty"`
}

// GetProductInfoResponse 获取产品信息响应
type GetProductInfoResponse struct {
	CommonResponse
	// 产品信息
	Result ProductInfo `json:"result"`
}

// ProductInfo 产品信息
type ProductInfo struct {
	// 产品ID
	ID int64 `json:"id"`
	// 名称
	Name string `json:"name"`
	// 卖家商品ID
	OfferID string `json:"offer_id"`
	// 条形码
	Barcode string `json:"barcode"`
	// 描述
	Description string `json:"description"`
	// 分类ID
	CategoryID int64 `json:"category_id"`
	// 状态
	State string `json:"state"`
	// 可见性
	Visible bool `json:"visible"`
	// 图片
	Images []ProductImage `json:"images"`
	// 创建时间
	CreatedAt time.Time `json:"created_at"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at"`
}

// ProductImage 产品图片
type ProductImage struct {
	// 文件名
	FileName string `json:"file_name"`
	// 是否默认
	Default bool `json:"default"`
	// 索引
	Index int `json:"index"`
}

// GetProductInfo 获取产品信息
func (s *ProductsService) GetProductInfo(ctx context.Context, params *GetProductInfoParams) (*GetProductInfoResponse, error) {
	result, err := s.Request("POST", "/v2/product/info", params)
	if err != nil {
		return nil, err
	}

	response := &GetProductInfoResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// ImportProductParams 导入产品参数
type ImportProductParams struct {
	// 产品列表
	Items []ImportProductItem `json:"items"`
}

// ImportProductItem 导入产品项
type ImportProductItem struct {
	// 卖家商品ID
	OfferID string `json:"offer_id"`
	// 名称
	Name string `json:"name"`
	// 描述
	Description string `json:"description"`
	// 分类ID
	CategoryID int64 `json:"category_id"`
	// 价格
	Price string `json:"price"`
	// 旧价格
	OldPrice string `json:"old_price,omitempty"`
	// 增值税
	VAT string `json:"vat"`
	// 图片
	Images []ProductImage `json:"images"`
	// 属性
	Attributes []ProductAttribute `json:"attributes"`
}

// ProductAttribute 产品属性
type ProductAttribute struct {
	// 属性ID
	ID int64 `json:"id"`
	// 值
	Values []ProductAttributeValue `json:"values"`
}

// ProductAttributeValue 产品属性值
type ProductAttributeValue struct {
	// 字典值ID
	DictionaryValueID int64 `json:"dictionary_value_id,omitempty"`
	// 值
	Value string `json:"value,omitempty"`
}

// ImportProductResponse 导入产品响应
type ImportProductResponse struct {
	CommonResponse
	// 任务ID
	TaskID int64 `json:"task_id"`
}

// ImportProduct 导入产品
func (s *ProductsService) ImportProduct(ctx context.Context, params *ImportProductParams) (*ImportProductResponse, error) {
	result, err := s.Request("POST", "/v1/product/import", params)
	if err != nil {
		return nil, err
	}

	response := &ImportProductResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// mapToStruct 将map转换为结构体的辅助方法
func (s *ProductsService) mapToStruct(data map[string]interface{}, target interface{}) error {
	// 这里可以使用json.Marshal/Unmarshal或者其他映射方法
	// 为了简化，这里返回nil，实际使用时需要实现具体的映射逻辑
	return nil
}
