package ozonapi

import (
	"context"
	"fmt"
)

// WarehousesService 仓库服务，参考ozon-client的Warehouses结构
type WarehousesService struct {
	*BaseService
}

// NewWarehousesService 创建仓库服务
func NewWarehousesService(baseService *BaseService) *WarehousesService {
	return &WarehousesService{
		BaseService: baseService,
	}
}

// GetWarehouseListParams 获取仓库列表参数
type GetWarehouseListParams struct {
	// 无参数
}

// GetWarehouseListResponse 获取仓库列表响应
type GetWarehouseListResponse struct {
	CommonResponse
	// 仓库列表
	Result []WarehouseInfo `json:"result"`
}

// WarehouseInfo 仓库信息
type WarehouseInfo struct {
	// 仓库ID
	ID int64 `json:"id"`
	// 仓库名称
	Name string `json:"name"`
	// 仓库状态
	Status string `json:"status"`
	// 仓库类型
	Type string `json:"type"`
	// 是否有入口点
	HasEntrypoint bool `json:"has_entrypoint"`
	// 是否可以打印贴纸
	CanPrintSticker bool `json:"can_print_sticker"`
	// 最小工作天数
	MinWorkingDays int `json:"min_working_days"`
	// 最大工作天数
	MaxWorkingDays int `json:"max_working_days"`
	// 工作时间
	WorkingHours string `json:"working_hours"`
	// 城市
	City string `json:"city"`
	// 地址
	Address string `json:"address"`
	// 货物限制
	CargoRestrictions string `json:"cargo_restrictions"`
}

// GetWarehouseList 获取仓库列表
func (s *WarehousesService) GetWarehouseList(ctx context.Context, params *GetWarehouseListParams) (*GetWarehouseListResponse, error) {
	result, err := s.Request("POST", "/v1/warehouse/list", params)
	if err != nil {
		return nil, err
	}

	response := &GetWarehouseListResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// GetFBOWarehouseListParams 获取FBO仓库列表参数
type GetFBOWarehouseListParams struct {
	// 按供应类型过滤
	FilterBySupplyType []string `json:"filter_by_supply_type,omitempty"`
	// 搜索文本
	Search string `json:"search,omitempty"`
}

// GetFBOWarehouseListResponse 获取FBO仓库列表响应
type GetFBOWarehouseListResponse struct {
	CommonResponse
	// 搜索结果
	Search []FBOWarehouseInfo `json:"search"`
}

// FBOWarehouseInfo FBO仓库信息
type FBOWarehouseInfo struct {
	// 地址
	Address string `json:"address"`
	// 坐标
	Coordinates FBOWarehouseCoordinates `json:"coordinates"`
	// 名称
	Name string `json:"name"`
	// 仓库ID
	WarehouseID int64 `json:"warehouse_id"`
	// 仓库类型
	WarehouseType string `json:"warehouse_type"`
}

// FBOWarehouseCoordinates FBO仓库坐标
type FBOWarehouseCoordinates struct {
	// 纬度
	Latitude float64 `json:"latitude"`
	// 经度
	Longitude float64 `json:"longitude"`
}

// GetFBOWarehouseList 搜索FBO仓库
func (s *WarehousesService) GetFBOWarehouseList(ctx context.Context, params *GetFBOWarehouseListParams) (*GetFBOWarehouseListResponse, error) {
	result, err := s.Request("POST", "/v1/warehouse/fbo/list", params)
	if err != nil {
		return nil, err
	}

	response := &GetFBOWarehouseListResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// GetDeliveryMethodListParams 获取配送方式列表参数
type GetDeliveryMethodListParams struct {
	// 过滤器
	Filter GetDeliveryMethodFilter `json:"filter,omitempty"`
	// 偏移量
	Offset int32 `json:"offset,omitempty"`
	// 限制数量
	Limit int32 `json:"limit,omitempty"`
}

// GetDeliveryMethodFilter 配送方式过滤器
type GetDeliveryMethodFilter struct {
	// 提供商ID
	ProviderID int64 `json:"provider_id,omitempty"`
	// 状态
	Status string `json:"status,omitempty"`
}

// GetDeliveryMethodListResponse 获取配送方式列表响应
type GetDeliveryMethodListResponse struct {
	CommonResponse
	// 配送方式列表
	Result []DeliveryMethodInfo `json:"result"`
}

// DeliveryMethodInfo 配送方式信息
type DeliveryMethodInfo struct {
	// ID
	ID int64 `json:"id"`
	// 名称
	Name string `json:"name"`
	// 提供商ID
	ProviderID int64 `json:"provider_id"`
	// 提供商名称
	ProviderName string `json:"provider_name"`
	// 状态
	Status string `json:"status"`
	// 仓库ID
	WarehouseID int64 `json:"warehouse_id"`
	// 仓库名称
	WarehouseName string `json:"warehouse_name"`
}

// GetDeliveryMethodList 获取配送方式列表
func (s *WarehousesService) GetDeliveryMethodList(ctx context.Context, params *GetDeliveryMethodListParams) (*GetDeliveryMethodListResponse, error) {
	result, err := s.Request("POST", "/v1/delivery-method/list", params)
	if err != nil {
		return nil, err
	}

	response := &GetDeliveryMethodListResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// ActivateDeliveryMethodParams 激活配送方式参数
type ActivateDeliveryMethodParams struct {
	// 配送方式ID
	DeliveryMethodID int64 `json:"delivery_method_id"`
	// 仓库ID
	WarehouseID int64 `json:"warehouse_id"`
}

// ActivateDeliveryMethodResponse 激活配送方式响应
type ActivateDeliveryMethodResponse struct {
	CommonResponse
	// 结果
	Result bool `json:"result"`
}

// ActivateDeliveryMethod 激活配送方式
func (s *WarehousesService) ActivateDeliveryMethod(ctx context.Context, params *ActivateDeliveryMethodParams) (*ActivateDeliveryMethodResponse, error) {
	result, err := s.Request("POST", "/v1/delivery-method/activate", params)
	if err != nil {
		return nil, err
	}

	response := &ActivateDeliveryMethodResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// DeactivateDeliveryMethodParams 停用配送方式参数
type DeactivateDeliveryMethodParams struct {
	// 配送方式ID
	DeliveryMethodID int64 `json:"delivery_method_id"`
	// 仓库ID
	WarehouseID int64 `json:"warehouse_id"`
}

// DeactivateDeliveryMethodResponse 停用配送方式响应
type DeactivateDeliveryMethodResponse struct {
	CommonResponse
	// 结果
	Result bool `json:"result"`
}

// DeactivateDeliveryMethod 停用配送方式
func (s *WarehousesService) DeactivateDeliveryMethod(ctx context.Context, params *DeactivateDeliveryMethodParams) (*DeactivateDeliveryMethodResponse, error) {
	result, err := s.Request("POST", "/v1/delivery-method/deactivate", params)
	if err != nil {
		return nil, err
	}

	response := &DeactivateDeliveryMethodResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// mapToStruct 将map转换为结构体的辅助方法
func (s *WarehousesService) mapToStruct(data map[string]interface{}, target interface{}) error {
	// 这里可以使用json.Marshal/Unmarshal或者其他映射方法
	// 为了简化，这里返回nil，实际使用时需要实现具体的映射逻辑
	return nil
}
