package ozonapi

import (
	"time"

	"github.com/go-resty/resty/v2"
)

const (
	defaultTimeout = 10 * time.Second
	defaultHost    = "https://api-seller.ozon.ru"
)

// Client Ozon API 客户端，参考ozon-client结构
type Client struct {
	httpClient *resty.Client // HTTP 请求客户端
	config     *Config       // 客户端配置

	// 主要服务模块 (参考ozon-client结构)
	Analytics     *AnalyticsService
	FBO           *FBOService
	FBS           *FBSService
	Finance       *FinanceService
	Products      *ProductsService
	Promotions    *PromotionsService
	Rating        *RatingService
	Warehouses    *WarehousesService
	Returns       *ReturnsService
	Reports       *ReportsService
	Cancellations *CancellationsService
	Categories    *CategoriesService
	Polygons      *PolygonsService
	Invoices      *InvoicesService
	Brands        *BrandsService
	Chats         *ChatsService
	Certificates  *CertificatesService
	Strategies    *StrategiesService
	Barcodes      *BarcodesService
	Passes        *PassesService
	Clusters      *ClustersService
	Quants        *QuantsService
	Reviews       *ReviewsService
}

// Config 客户端配置
type Config struct {
	ClientID string
	APIKey   string
	Host     string
	Timeout  time.Duration
}

// ClientOption 客户端配置选项
type ClientOption func(*Client)

// WithTimeout 设置超时时间
func WithTimeout(timeout time.Duration) ClientOption {
	return func(c *Client) {
		c.httpClient.SetTimeout(timeout)
	}
}

// NewClient 创建新的 Ozon API 客户端
func NewClient(apiKey, clientId string, opts ...ClientOption) *Client {
	rc := resty.New()
	rc.SetTimeout(defaultTimeout)
	rc.SetHeader("Client-Id", clientId) // Ozon API需要Client-Id header
	rc.SetHeader("Api-Key", apiKey)     // Ozon API需要Api-Key header
	rc.SetHeader("Host", "api-seller.ozon.ru")
	rc.SetHeader("Accept", "application/json")
	rc.SetBaseURL("https://api-seller.ozon.ru")
	rc.SetHeader("Content-Type", "application/json")
	rc.SetProxy("socks5://lens:ls3903850@*************:23481")
	c := &Client{
		httpClient: rc,
	}

	// 应用自定义选项
	for _, opt := range opts {
		opt(c)
	}

	// 初始化各个服务
	// c.Products = newProductService(c)
	// c.Orders = newOrderService(c)
	// c.Analytics = newAnalyticsService(c)
	c.FBO = newFBOService(c)

	return c
}

// GetHTTPClient 获取HTTP客户端
func (c *Client) GetHTTPClient() *resty.Client {
	return c.httpClient
}
