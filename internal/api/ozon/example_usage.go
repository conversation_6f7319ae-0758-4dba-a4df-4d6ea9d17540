package ozonapi

import (
	"context"
	"fmt"
	"log"
	"time"
)

// ExampleUsage 展示如何使用Ozon API客户端的示例
func ExampleUsage() {
	// 创建客户端
	client := NewClient("your-api-key", "your-client-id",
		WithTimeout(30*time.Second),
		WithProxy("socks5://lens:ls3903850@185.22.152.62:23481"),
	)

	ctx := context.Background()

	// 示例1: 获取产品库存信息
	fmt.Println("=== 获取产品库存信息 ===")
	stockParams := &GetStocksInfoParams{
		Limit: 100,
		Filter: GetStocksInfoFilter{
			Visibility: string(VisibilityVisible),
		},
	}

	stockResponse, err := client.Products.GetStocksInfo(ctx, stockParams)
	if err != nil {
		log.Printf("获取库存信息失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个产品的库存信息\n", len(stockResponse.Items))
		for _, item := range stockResponse.Items {
			fmt.Printf("产品ID: %d, OfferID: %s\n", item.ProductID, item.OfferID)
		}
	}

	// 示例2: 获取产品分类树
	fmt.Println("\n=== 获取产品分类树 ===")
	treeParams := &GetProductTreeParams{
		Language: LanguageRU,
	}

	treeResponse, err := client.Categories.Tree(ctx, treeParams)
	if err != nil {
		log.Printf("获取分类树失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个顶级分类\n", len(treeResponse.Result))
		for _, category := range treeResponse.Result {
			fmt.Printf("分类ID: %d, 名称: %s, 子分类数: %d\n", 
				category.DescriptionCategoryID, category.CategoryName, len(category.Children))
		}
	}

	// 示例3: 获取仓库列表
	fmt.Println("\n=== 获取仓库列表 ===")
	warehouseParams := &GetWarehouseListParams{}

	warehouseResponse, err := client.Warehouses.GetWarehouseList(ctx, warehouseParams)
	if err != nil {
		log.Printf("获取仓库列表失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个仓库\n", len(warehouseResponse.Result))
		for _, warehouse := range warehouseResponse.Result {
			fmt.Printf("仓库ID: %d, 名称: %s, 状态: %s\n", 
				warehouse.ID, warehouse.Name, warehouse.Status)
		}
	}

	// 示例4: 获取分析数据
	fmt.Println("\n=== 获取分析数据 ===")
	now := time.Now()
	dateFrom := &TimeFormat{Time: now.AddDate(0, 0, -30)} // 30天前
	dateTo := &TimeFormat{Time: now}

	analyticsParams := &GetAnalyticsDataParams{
		DateFrom: dateFrom,
		DateTo:   dateTo,
		Dimension: []GetAnalyticsDataDimension{
			DimensionSKU,
			DimensionDay,
		},
		Metrics: []GetAnalyticsDataFilterMetric{
			MetricRevenue,
			MetricOrderedUnits,
		},
		Limit: 100,
	}

	analyticsResponse, err := client.Analytics.GetAnalyticsData(ctx, analyticsParams)
	if err != nil {
		log.Printf("获取分析数据失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 条分析数据\n", len(analyticsResponse.Result.Data))
	}

	// 示例5: 获取FBS发货单列表
	fmt.Println("\n=== 获取FBS发货单列表 ===")
	fbsParams := &GetFBSPostingListParams{
		Filter: GetFBSPostingListFilter{
			Since: now.AddDate(0, 0, -7).Format("2006-01-02T15:04:05Z"), // 7天前
			To:    now.Format("2006-01-02T15:04:05Z"),
		},
		Limit: 50,
		With: GetFBSPostingListWith{
			AnalyticsData: true,
			FinancialData: true,
		},
	}

	fbsResponse, err := client.FBS.GetFBSPostingList(ctx, fbsParams)
	if err != nil {
		log.Printf("获取FBS发货单列表失败: %v", err)
	} else {
		fmt.Printf("获取到 %d 个FBS发货单\n", len(fbsResponse.Result.Postings))
		for _, posting := range fbsResponse.Result.Postings {
			fmt.Printf("发货单号: %s, 状态: %s, 产品数: %d\n", 
				posting.PostingNumber, posting.Status, len(posting.Products))
		}
	}

	// 示例6: 使用FBO服务（保持向后兼容）
	fmt.Println("\n=== 使用FBO服务 ===")
	clusterRequest := &ClusterListRequest{
		ClusterType: "CLUSTER_TYPE_OZON",
	}

	clusterResponse, err := client.FBO.GetClusterAndWarehouseList(clusterRequest)
	if err != nil {
		log.Printf("获取集群和仓库列表失败: %v", err)
	} else {
		fmt.Printf("获取到集群信息: %s\n", clusterResponse.Name)
	}
}

// ExampleErrorHandling 展示错误处理的示例
func ExampleErrorHandling() {
	client := NewClient("invalid-api-key", "invalid-client-id")
	ctx := context.Background()

	// 尝试获取产品信息（应该会失败）
	params := &GetStocksInfoParams{
		Limit: 10,
	}

	_, err := client.Products.GetStocksInfo(ctx, params)
	if err != nil {
		// 检查是否是特定的Ozon异常
		switch e := err.(type) {
		case *AccessDeniedException:
			fmt.Printf("访问被拒绝: %s\n", e.GetMessage())
		case *ValidationException:
			fmt.Printf("验证失败: %s\n", e.GetMessage())
		case *OzonSellerException:
			fmt.Printf("Ozon API错误: %s (代码: %d)\n", e.GetMessage(), e.GetCode())
			if details := e.GetDetails(); len(details) > 0 {
				fmt.Printf("错误详情: %+v\n", details)
			}
		default:
			fmt.Printf("其他错误: %v\n", err)
		}
	}
}

// ExampleCustomConfiguration 展示自定义配置的示例
func ExampleCustomConfiguration() {
	// 使用自定义配置创建客户端
	client := NewClient("your-api-key", "your-client-id",
		WithTimeout(60*time.Second),                    // 设置60秒超时
		WithHost("https://api-seller.ozon.ru"),         // 自定义主机
		WithProxy("http://proxy.example.com:8080"),     // 设置HTTP代理
	)

	// 获取客户端配置
	config := client.GetConfig()
	fmt.Printf("客户端配置:\n")
	fmt.Printf("  Client ID: %s\n", config.ClientID)
	fmt.Printf("  Host: %s\n", config.Host)
	fmt.Printf("  Timeout: %v\n", config.Timeout)

	// 获取HTTP客户端进行更高级的配置
	httpClient := client.GetHTTPClient()
	httpClient.SetRetryCount(3)
	httpClient.SetRetryWaitTime(5 * time.Second)
	httpClient.SetRetryMaxWaitTime(20 * time.Second)

	fmt.Println("已配置重试机制")
}

// ExampleBatchOperations 展示批量操作的示例
func ExampleBatchOperations() {
	client := NewClient("your-api-key", "your-client-id")
	ctx := context.Background()

	// 批量获取多个产品的信息
	productIDs := []int64{123456, 789012, 345678}
	
	for _, productID := range productIDs {
		params := &GetProductInfoParams{
			ProductID: productID,
		}
		
		response, err := client.Products.GetProductInfo(ctx, params)
		if err != nil {
			log.Printf("获取产品 %d 信息失败: %v", productID, err)
			continue
		}
		
		fmt.Printf("产品 %d: %s\n", response.Result.ID, response.Result.Name)
	}

	// 批量发货FBS订单
	postingNumbers := []string{"posting1", "posting2", "posting3"}
	
	shipParams := &ShipFBSPostingParams{
		PostingNumber: postingNumbers,
		With: ShipFBSPostingWith{
			AdditionalData: true,
		},
	}
	
	shipResponse, err := client.FBS.ShipFBSPosting(ctx, shipParams)
	if err != nil {
		log.Printf("批量发货失败: %v", err)
	} else {
		for _, result := range shipResponse.Result {
			fmt.Printf("发货单 %s: %s\n", result.PostingNumber, result.Status)
		}
	}
}
