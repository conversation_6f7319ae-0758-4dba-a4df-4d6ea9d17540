package ozonapi

import (
	"encoding/json"
	"fmt"
)

// OzonSellerException Ozon卖家API异常，对应PHP的OzonSellerException
type OzonSellerException struct {
	message string
	code    int
	details map[string]interface{}
}

// NewOzonSellerException 创建Ozon卖家异常
func NewOzonSellerException(message string, code int, details map[string]interface{}) *OzonSellerException {
	if details == nil {
		details = make(map[string]interface{})
	}
	return &OzonSellerException{
		message: message,
		code:    code,
		details: details,
	}
}

// Error 实现error接口
func (e *OzonSellerException) Error() string {
	if len(e.details) > 0 {
		detailsJSON, _ := json.Marshal(e.details)
		return fmt.Sprintf("%s\nData: %s", e.message, string(detailsJSON))
	}
	return e.message
}

// GetMessage 获取错误消息
func (e *OzonSellerException) GetMessage() string {
	return e.message
}

// GetCode 获取错误代码
func (e *OzonSellerException) GetCode() int {
	return e.code
}

// GetDetails 获取错误详情
func (e *OzonSellerException) GetDetails() map[string]interface{} {
	return e.details
}

// GetData 获取错误数据（向后兼容）
// Deprecated: 使用GetDetails()方法
func (e *OzonSellerException) GetData() map[string]interface{} {
	return e.details
}

// AccessDeniedException 访问拒绝异常
type AccessDeniedException struct {
	*OzonSellerException
}

// NewAccessDeniedException 创建访问拒绝异常
func NewAccessDeniedException(message string, details map[string]interface{}) *AccessDeniedException {
	return &AccessDeniedException{
		OzonSellerException: NewOzonSellerException(message, 403, details),
	}
}

// NotFoundException 未找到异常
type NotFoundException struct {
	*OzonSellerException
}

// NewNotFoundException 创建未找到异常
func NewNotFoundException(message string, details map[string]interface{}) *NotFoundException {
	return &NotFoundException{
		OzonSellerException: NewOzonSellerException(message, 404, details),
	}
}

// NotFoundInSortingCenterException 分拣中心未找到异常
type NotFoundInSortingCenterException struct {
	*OzonSellerException
}

// NewNotFoundInSortingCenterException 创建分拣中心未找到异常
func NewNotFoundInSortingCenterException(message string, details map[string]interface{}) *NotFoundInSortingCenterException {
	return &NotFoundInSortingCenterException{
		OzonSellerException: NewOzonSellerException(message, 404, details),
	}
}

// InternalException 内部错误异常
type InternalException struct {
	*OzonSellerException
}

// NewInternalException 创建内部错误异常
func NewInternalException(message string, details map[string]interface{}) *InternalException {
	return &InternalException{
		OzonSellerException: NewOzonSellerException(message, 500, details),
	}
}

// ValidationException 验证异常
type ValidationException struct {
	*OzonSellerException
}

// NewValidationException 创建验证异常
func NewValidationException(message string, details map[string]interface{}) *ValidationException {
	return &ValidationException{
		OzonSellerException: NewOzonSellerException(message, 400, details),
	}
}

// BadRequestException 错误请求异常
type BadRequestException struct {
	*OzonSellerException
}

// NewBadRequestException 创建错误请求异常
func NewBadRequestException(message string, details map[string]interface{}) *BadRequestException {
	return &BadRequestException{
		OzonSellerException: NewOzonSellerException(message, 400, details),
	}
}

// UnauthorizedException 未授权异常
type UnauthorizedException struct {
	*OzonSellerException
}

// NewUnauthorizedException 创建未授权异常
func NewUnauthorizedException(message string, details map[string]interface{}) *UnauthorizedException {
	return &UnauthorizedException{
		OzonSellerException: NewOzonSellerException(message, 401, details),
	}
}

// ForbiddenException 禁止访问异常
type ForbiddenException struct {
	*OzonSellerException
}

// NewForbiddenException 创建禁止访问异常
func NewForbiddenException(message string, details map[string]interface{}) *ForbiddenException {
	return &ForbiddenException{
		OzonSellerException: NewOzonSellerException(message, 403, details),
	}
}

// ConflictException 冲突异常
type ConflictException struct {
	*OzonSellerException
}

// NewConflictException 创建冲突异常
func NewConflictException(message string, details map[string]interface{}) *ConflictException {
	return &ConflictException{
		OzonSellerException: NewOzonSellerException(message, 409, details),
	}
}

// TooManyRequestsException 请求过多异常
type TooManyRequestsException struct {
	*OzonSellerException
}

// NewTooManyRequestsException 创建请求过多异常
func NewTooManyRequestsException(message string, details map[string]interface{}) *TooManyRequestsException {
	return &TooManyRequestsException{
		OzonSellerException: NewOzonSellerException(message, 429, details),
	}
}

// ServiceUnavailableException 服务不可用异常
type ServiceUnavailableException struct {
	*OzonSellerException
}

// NewServiceUnavailableException 创建服务不可用异常
func NewServiceUnavailableException(message string, details map[string]interface{}) *ServiceUnavailableException {
	return &ServiceUnavailableException{
		OzonSellerException: NewOzonSellerException(message, 503, details),
	}
}

// TimeoutException 超时异常
type TimeoutException struct {
	*OzonSellerException
}

// NewTimeoutException 创建超时异常
func NewTimeoutException(message string, details map[string]interface{}) *TimeoutException {
	return &TimeoutException{
		OzonSellerException: NewOzonSellerException(message, 408, details),
	}
}

// NetworkException 网络异常
type NetworkException struct {
	*OzonSellerException
}

// NewNetworkException 创建网络异常
func NewNetworkException(message string, details map[string]interface{}) *NetworkException {
	return &NetworkException{
		OzonSellerException: NewOzonSellerException(message, 0, details),
	}
}

// ParseException 解析异常
type ParseException struct {
	*OzonSellerException
}

// NewParseException 创建解析异常
func NewParseException(message string, details map[string]interface{}) *ParseException {
	return &ParseException{
		OzonSellerException: NewOzonSellerException(message, 0, details),
	}
}

// IsRetryableError 检查错误是否可重试
func IsRetryableError(err error) bool {
	switch err.(type) {
	case *TooManyRequestsException:
		return true
	case *ServiceUnavailableException:
		return true
	case *TimeoutException:
		return true
	case *NetworkException:
		return true
	case *InternalException:
		return true
	default:
		return false
	}
}

// IsClientError 检查是否是客户端错误（4xx）
func IsClientError(err error) bool {
	if ozonErr, ok := err.(*OzonSellerException); ok {
		return ozonErr.GetCode() >= 400 && ozonErr.GetCode() < 500
	}
	return false
}

// IsServerError 检查是否是服务器错误（5xx）
func IsServerError(err error) bool {
	if ozonErr, ok := err.(*OzonSellerException); ok {
		return ozonErr.GetCode() >= 500 && ozonErr.GetCode() < 600
	}
	return false
}

// GetErrorCode 获取错误代码
func GetErrorCode(err error) int {
	if ozonErr, ok := err.(*OzonSellerException); ok {
		return ozonErr.GetCode()
	}
	return 0
}

// GetErrorMessage 获取错误消息
func GetErrorMessage(err error) string {
	if ozonErr, ok := err.(*OzonSellerException); ok {
		return ozonErr.GetMessage()
	}
	return err.Error()
}

// GetErrorDetails 获取错误详情
func GetErrorDetails(err error) map[string]interface{} {
	if ozonErr, ok := err.(*OzonSellerException); ok {
		return ozonErr.GetDetails()
	}
	return nil
}
