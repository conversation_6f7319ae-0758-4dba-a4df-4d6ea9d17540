# Ozon API Go客户端

这是一个完整的Ozon卖家API Go客户端，参考了ozon-client的结构设计，保持了原有的代码风格。

## 特性

- 🚀 完整的Ozon卖家API支持
- 🔧 模块化设计，易于扩展
- 🛡️ 完善的错误处理机制
- 📦 支持所有主要服务模块
- 🔄 向后兼容原有FBO服务
- 🌐 支持代理和自定义配置
- 📝 详细的类型定义和文档

## 支持的服务模块

### 核心服务
- **Products** - 商品管理
- **Analytics** - 数据分析
- **Categories** - 分类管理
- **Warehouses** - 仓库管理
- **FBS** - FBS发货管理
- **FBO** - FBO发货管理（向后兼容）

### 其他服务
- **Finance** - 财务管理
- **Promotions** - 促销管理
- **Rating** - 评级管理
- **Returns** - 退货管理
- **Reports** - 报告管理
- **Cancellations** - 取消管理
- **Polygons** - 配送区域管理
- **Invoices** - 发票管理
- **Brands** - 品牌管理
- **Chats** - 聊天管理
- **Certificates** - 证书管理
- **Strategies** - 策略管理
- **Barcodes** - 条形码管理
- **Passes** - 通行证管理
- **Clusters** - 集群管理
- **Quants** - 数量管理
- **Reviews** - 评论管理

## 快速开始

### 安装

```go
import "lens/internal/api/ozon"
```

### 基本使用

```go
package main

import (
    "context"
    "fmt"
    "time"
    
    ozonapi "lens/internal/api/ozon"
)

func main() {
    // 创建客户端
    client := ozonapi.NewClient("your-api-key", "your-client-id",
        ozonapi.WithTimeout(30*time.Second),
        ozonapi.WithProxy("socks5://user:pass@proxy:port"),
    )
    
    ctx := context.Background()
    
    // 获取产品库存信息
    stockParams := &ozonapi.GetStocksInfoParams{
        Limit: 100,
        Filter: ozonapi.GetStocksInfoFilter{
            Visibility: string(ozonapi.VisibilityVisible),
        },
    }
    
    response, err := client.Products.GetStocksInfo(ctx, stockParams)
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("获取到 %d 个产品的库存信息\n", len(response.Items))
}
```

## 详细使用示例

### 1. 产品管理

```go
// 获取产品列表
listParams := &ozonapi.GetProductListParams{
    Filter: ozonapi.GetProductListFilter{
        Visibility: ozonapi.VisibilityVisible,
    },
    Limit: 100,
}

listResponse, err := client.Products.GetProductList(ctx, listParams)
if err != nil {
    log.Fatal(err)
}

// 获取产品详细信息
infoParams := &ozonapi.GetProductInfoParams{
    ProductID: 123456,
}

infoResponse, err := client.Products.GetProductInfo(ctx, infoParams)
if err != nil {
    log.Fatal(err)
}

// 导入产品
importParams := &ozonapi.ImportProductParams{
    Items: []ozonapi.ImportProductItem{
        {
            OfferID:     "my-offer-1",
            Name:        "测试产品",
            Description: "产品描述",
            CategoryID:  17029016,
            Price:       "1000",
            VAT:         "0.2",
        },
    },
}

importResponse, err := client.Products.ImportProduct(ctx, importParams)
if err != nil {
    log.Fatal(err)
}
```

### 2. 分析数据

```go
// 获取分析数据
now := time.Now()
analyticsParams := &ozonapi.GetAnalyticsDataParams{
    DateFrom: &ozonapi.TimeFormat{Time: now.AddDate(0, 0, -30)},
    DateTo:   &ozonapi.TimeFormat{Time: now},
    Dimension: []ozonapi.GetAnalyticsDataDimension{
        ozonapi.DimensionSKU,
        ozonapi.DimensionDay,
    },
    Metrics: []ozonapi.GetAnalyticsDataFilterMetric{
        ozonapi.MetricRevenue,
        ozonapi.MetricOrderedUnits,
    },
    Limit: 100,
}

analyticsResponse, err := client.Analytics.GetAnalyticsData(ctx, analyticsParams)
if err != nil {
    log.Fatal(err)
}

// 获取仓库库存报告
stockParams := &ozonapi.GetStockOnWarehousesParams{
    Limit:         100,
    WarehouseType: ozonapi.WarehouseTypeFBS,
}

stockResponse, err := client.Analytics.GetStockOnWarehouses(ctx, stockParams)
if err != nil {
    log.Fatal(err)
}
```

### 3. FBS发货管理

```go
// 获取FBS发货单列表
fbsParams := &ozonapi.GetFBSPostingListParams{
    Filter: ozonapi.GetFBSPostingListFilter{
        Status: "awaiting_packaging",
        Since:  time.Now().AddDate(0, 0, -7).Format("2006-01-02T15:04:05Z"),
        To:     time.Now().Format("2006-01-02T15:04:05Z"),
    },
    Limit: 50,
    With: ozonapi.GetFBSPostingListWith{
        AnalyticsData: true,
        FinancialData: true,
    },
}

fbsResponse, err := client.FBS.GetFBSPostingList(ctx, fbsParams)
if err != nil {
    log.Fatal(err)
}

// 发货FBS订单
shipParams := &ozonapi.ShipFBSPostingParams{
    PostingNumber: []string{"posting1", "posting2"},
    With: ozonapi.ShipFBSPostingWith{
        AdditionalData: true,
    },
}

shipResponse, err := client.FBS.ShipFBSPosting(ctx, shipParams)
if err != nil {
    log.Fatal(err)
}
```

### 4. 分类管理

```go
// 获取分类树
treeParams := &ozonapi.GetProductTreeParams{
    Language: ozonapi.LanguageRU,
}

treeResponse, err := client.Categories.Tree(ctx, treeParams)
if err != nil {
    log.Fatal(err)
}

// 获取分类属性
attrParams := &ozonapi.GetCategoryAttributesParams{
    DescriptionCategoryID: 17029016,
    TypeID:                1000,
    Language:              ozonapi.LanguageRU,
}

attrResponse, err := client.Categories.GetCategoryAttributes(ctx, attrParams)
if err != nil {
    log.Fatal(err)
}
```

## 错误处理

客户端提供了完善的错误处理机制：

```go
_, err := client.Products.GetStocksInfo(ctx, params)
if err != nil {
    switch e := err.(type) {
    case *ozonapi.AccessDeniedException:
        fmt.Printf("访问被拒绝: %s\n", e.GetMessage())
    case *ozonapi.ValidationException:
        fmt.Printf("验证失败: %s\n", e.GetMessage())
    case *ozonapi.NotFoundException:
        fmt.Printf("资源未找到: %s\n", e.GetMessage())
    case *ozonapi.OzonSellerException:
        fmt.Printf("Ozon API错误: %s (代码: %d)\n", e.GetMessage(), e.GetCode())
        if details := e.GetDetails(); len(details) > 0 {
            fmt.Printf("错误详情: %+v\n", details)
        }
    default:
        fmt.Printf("其他错误: %v\n", err)
    }
}
```

## 配置选项

```go
client := ozonapi.NewClient("api-key", "client-id",
    // 设置超时时间
    ozonapi.WithTimeout(60*time.Second),
    
    // 设置自定义主机
    ozonapi.WithHost("https://api-seller.ozon.ru"),
    
    // 设置代理
    ozonapi.WithProxy("socks5://user:pass@proxy:port"),
)

// 获取HTTP客户端进行更高级配置
httpClient := client.GetHTTPClient()
httpClient.SetRetryCount(3)
httpClient.SetRetryWaitTime(5 * time.Second)
```

## 向后兼容

原有的FBO服务仍然可以使用：

```go
// 使用原有的FBO服务
clusterRequest := &ozonapi.ClusterListRequest{
    ClusterType: "CLUSTER_TYPE_OZON",
}

clusterResponse, err := client.FBO.GetClusterAndWarehouseList(clusterRequest)
if err != nil {
    log.Fatal(err)
}
```

## 贡献

欢迎提交Issue和Pull Request来改进这个客户端。

## 许可证

MIT License
