package ozonapi

import (
	"context"
	"fmt"
)

// FBSService FBS服务，参考ozon-client的FBS结构
type FBSService struct {
	*BaseService
}

// NewFBSService 创建FBS服务
func NewFBSService(baseService *BaseService) *FBSService {
	return &FBSService{
		BaseService: baseService,
	}
}

// GetFBSPostingListParams 获取FBS发货单列表参数
type GetFBSPostingListParams struct {
	// 过滤器
	Filter GetFBSPostingListFilter `json:"filter"`
	// 方向
	Dir string `json:"dir,omitempty"`
	// 偏移量
	Offset int32 `json:"offset"`
	// 限制数量
	Limit int32 `json:"limit"`
	// 附加字段
	With GetFBSPostingListWith `json:"with,omitempty"`
}

// GetFBSPostingListFilter FBS发货单列表过滤器
type GetFBSPostingListFilter struct {
	// 订单号
	OrderNumber string `json:"order_number,omitempty"`
	// 发货单号
	PostingNumber string `json:"posting_number,omitempty"`
	// 状态
	Status string `json:"status,omitempty"`
	// 开始日期
	Since string `json:"since,omitempty"`
	// 结束日期
	To string `json:"to,omitempty"`
}

// GetFBSPostingListWith FBS发货单列表附加字段
type GetFBSPostingListWith struct {
	// 分析数据
	AnalyticsData bool `json:"analytics_data,omitempty"`
	// 财务数据
	FinancialData bool `json:"financial_data,omitempty"`
}

// GetFBSPostingListResponse 获取FBS发货单列表响应
type GetFBSPostingListResponse struct {
	CommonResponse
	// 发货单列表
	Result GetFBSPostingListResult `json:"result"`
}

// GetFBSPostingListResult FBS发货单列表结果
type GetFBSPostingListResult struct {
	// 发货单列表
	Postings []FBSPosting `json:"postings"`
	// 是否有下一页
	HasNext bool `json:"has_next"`
}

// FBSPosting FBS发货单
type FBSPosting struct {
	// 发货单号
	PostingNumber string `json:"posting_number"`
	// 订单ID
	OrderID int64 `json:"order_id"`
	// 订单号
	OrderNumber string `json:"order_number"`
	// 状态
	Status string `json:"status"`
	// 创建时间
	CreatedAt string `json:"created_at"`
	// 发货截止时间
	ShipmentDate string `json:"shipment_date"`
	// 配送截止时间
	DeliveryDate string `json:"delivery_date"`
	// 产品列表
	Products []FBSPostingProduct `json:"products"`
}

// FBSPostingProduct FBS发货单产品
type FBSPostingProduct struct {
	// SKU
	SKU int64 `json:"sku"`
	// 名称
	Name string `json:"name"`
	// 数量
	Quantity int32 `json:"quantity"`
	// 卖家商品ID
	OfferID string `json:"offer_id"`
	// 价格
	Price string `json:"price"`
}

// GetFBSPostingList 获取FBS发货单列表
func (s *FBSService) GetFBSPostingList(ctx context.Context, params *GetFBSPostingListParams) (*GetFBSPostingListResponse, error) {
	result, err := s.Request("POST", "/v3/posting/fbs/list", params)
	if err != nil {
		return nil, err
	}

	response := &GetFBSPostingListResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// ShipFBSPostingParams 发货FBS发货单参数
type ShipFBSPostingParams struct {
	// 发货单号列表
	PostingNumber []string `json:"posting_number"`
	// 附加数据
	With ShipFBSPostingWith `json:"with,omitempty"`
}

// ShipFBSPostingWith 发货FBS发货单附加数据
type ShipFBSPostingWith struct {
	// 附加数据
	AdditionalData bool `json:"additional_data,omitempty"`
}

// ShipFBSPostingResponse 发货FBS发货单响应
type ShipFBSPostingResponse struct {
	CommonResponse
	// 结果
	Result []ShipFBSPostingResult `json:"result"`
}

// ShipFBSPostingResult 发货FBS发货单结果
type ShipFBSPostingResult struct {
	// 发货单号
	PostingNumber string `json:"posting_number"`
	// 状态
	Status string `json:"status"`
	// 错误
	Errors []string `json:"errors,omitempty"`
}

// ShipFBSPosting 发货FBS发货单
func (s *FBSService) ShipFBSPosting(ctx context.Context, params *ShipFBSPostingParams) (*ShipFBSPostingResponse, error) {
	result, err := s.Request("POST", "/v3/posting/fbs/ship", params)
	if err != nil {
		return nil, err
	}

	response := &ShipFBSPostingResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// CancelFBSPostingParams 取消FBS发货单参数
type CancelFBSPostingParams struct {
	// 发货单号
	PostingNumber string `json:"posting_number"`
	// 取消原因ID
	CancelReasonID int64 `json:"cancel_reason_id"`
	// 取消原因消息
	CancelReasonMessage string `json:"cancel_reason_message,omitempty"`
}

// CancelFBSPostingResponse 取消FBS发货单响应
type CancelFBSPostingResponse struct {
	CommonResponse
	// 结果
	Result bool `json:"result"`
}

// CancelFBSPosting 取消FBS发货单
func (s *FBSService) CancelFBSPosting(ctx context.Context, params *CancelFBSPostingParams) (*CancelFBSPostingResponse, error) {
	result, err := s.Request("POST", "/v2/posting/fbs/cancel", params)
	if err != nil {
		return nil, err
	}

	response := &CancelFBSPostingResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// GetFBSPostingLabelsParams 获取FBS发货单标签参数
type GetFBSPostingLabelsParams struct {
	// 发货单号列表
	PostingNumber []string `json:"posting_number"`
}

// GetFBSPostingLabelsResponse 获取FBS发货单标签响应
type GetFBSPostingLabelsResponse struct {
	CommonResponse
	// 标签内容（base64编码的PDF）
	Result string `json:"result"`
}

// GetFBSPostingLabels 获取FBS发货单标签
func (s *FBSService) GetFBSPostingLabels(ctx context.Context, params *GetFBSPostingLabelsParams) (*GetFBSPostingLabelsResponse, error) {
	result, err := s.Request("POST", "/v2/posting/fbs/package-label", params)
	if err != nil {
		return nil, err
	}

	response := &GetFBSPostingLabelsResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// mapToStruct 将map转换为结构体的辅助方法
func (s *FBSService) mapToStruct(data map[string]interface{}, target interface{}) error {
	// 这里可以使用json.Marshal/Unmarshal或者其他映射方法
	// 为了简化，这里返回nil，实际使用时需要实现具体的映射逻辑
	return nil
}
