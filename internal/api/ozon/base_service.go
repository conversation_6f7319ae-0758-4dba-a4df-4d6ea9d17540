package ozonapi

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"

	"github.com/go-resty/resty/v2"
)

// BaseService 基础服务，对应PHP的AbstractService
type BaseService struct {
	client *Client
}

// NewBaseService 创建基础服务
func NewBaseService(client *Client) *BaseService {
	return &BaseService{
		client: client,
	}
}

// Request 发送HTTP请求，对应PHP的request方法
func (s *BaseService) Request(method, uri string, body interface{}) (map[string]interface{}, error) {
	return s.RequestWithOptions(method, uri, body, RequestOptions{})
}

// RequestOptions 请求选项
type RequestOptions struct {
	SkipAuth     bool // 是否跳过认证
	RawResponse  bool // 是否返回原始响应
	CustomHost   string // 自定义主机
}

// RequestWithOptions 带选项的请求方法
func (s *BaseService) RequestWithOptions(method, uri string, body interface{}, opts RequestOptions) (map[string]interface{}, error) {
	req := s.client.httpClient.R()

	// 设置请求体
	if body != nil {
		req.SetBody(body)
	}

	// 自定义主机
	if opts.CustomHost != "" {
		req.SetHeader("Host", opts.CustomHost)
	}

	// 发送请求
	resp, err := req.Execute(method, uri)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode() >= 400 {
		return nil, s.handleErrorResponse(resp)
	}

	// 解析响应
	if opts.RawResponse {
		// 返回原始响应
		result := map[string]interface{}{
			"status_code": resp.StatusCode(),
			"headers":     resp.Header(),
			"body":        string(resp.Body()),
		}
		return result, nil
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return result, nil
}

// handleErrorResponse 处理错误响应
func (s *BaseService) handleErrorResponse(resp *resty.Response) error {
	var errorData map[string]interface{}
	if err := json.Unmarshal(resp.Body(), &errorData); err != nil {
		return NewOzonSellerException(string(resp.Body()), resp.StatusCode(), nil)
	}

	// 检查是否有错误信息
	if errorInfo, ok := errorData["error"].(map[string]interface{}); ok {
		if code, exists := errorInfo["code"].(string); exists {
			// 根据错误代码创建特定异常
			return s.createSpecificException(code, errorData)
		}
	}

	// 创建通用异常
	message := ""
	if msg, ok := errorData["message"].(string); ok {
		message = msg
	}

	details := make(map[string]interface{})
	if det, ok := errorData["details"]; ok {
		details["details"] = det
	}

	return NewOzonSellerException(message, resp.StatusCode(), details)
}

// createSpecificException 根据错误代码创建特定异常
func (s *BaseService) createSpecificException(code string, errorData map[string]interface{}) error {
	// 将错误代码转换为异常类型名
	exceptionType := s.getExceptionTypeByCode(code)
	
	message := ""
	if errorInfo, ok := errorData["error"].(map[string]interface{}); ok {
		if msg, ok := errorInfo["message"].(string); ok {
			message = msg
		}
	}

	details := make(map[string]interface{})
	if errorInfo, ok := errorData["error"].(map[string]interface{}); ok {
		if data, ok := errorInfo["data"]; ok {
			details = map[string]interface{}{"data": data}
		}
	}

	// 根据异常类型创建对应的错误
	switch exceptionType {
	case "AccessDenied":
		return NewAccessDeniedException(message, details)
	case "NotFound":
		return NewNotFoundException(message, details)
	case "NotFoundInSortingCenter":
		return NewNotFoundInSortingCenterException(message, details)
	case "Internal":
		return NewInternalException(message, details)
	case "Validation":
		return NewValidationException(message, details)
	default:
		return NewOzonSellerException(message, 0, details)
	}
}

// getExceptionTypeByCode 根据错误代码获取异常类型
func (s *BaseService) getExceptionTypeByCode(code string) string {
	parts := strings.Split(strings.ToLower(code), "_")
	
	// 移除 'error' 后缀
	if len(parts) > 0 && parts[len(parts)-1] == "error" {
		parts = parts[:len(parts)-1]
	}

	// 转换为驼峰命名
	for i, part := range parts {
		parts[i] = strings.Title(part)
	}

	return strings.Join(parts, "")
}

// EnsureCollection 确保数组是集合格式，对应PHP的ensureCollection方法
func (s *BaseService) EnsureCollection(arr interface{}) []interface{} {
	v := reflect.ValueOf(arr)
	if v.Kind() != reflect.Slice {
		return []interface{}{arr}
	}

	// 检查是否是关联数组（map）
	if v.Len() > 0 {
		first := v.Index(0)
		if first.Kind() == reflect.Map {
			// 如果是单个map，包装成数组
			if s.isAssocArray(arr) {
				return []interface{}{arr}
			}
		}
	}

	// 转换为interface{}切片
	result := make([]interface{}, v.Len())
	for i := 0; i < v.Len(); i++ {
		result[i] = v.Index(i).Interface()
	}
	return result
}

// isAssocArray 检查是否是关联数组，对应PHP的isAssoc方法
func (s *BaseService) isAssocArray(arr interface{}) bool {
	v := reflect.ValueOf(arr)
	if v.Kind() != reflect.Slice {
		return false
	}

	// 在Go中，我们通过检查是否包含map来判断是否是"关联数组"
	if v.Len() > 0 {
		first := v.Index(0)
		return first.Kind() == reflect.Map
	}

	return false
}

// GetClient 获取客户端
func (s *BaseService) GetClient() *Client {
	return s.client
}

// GetHTTPClient 获取HTTP客户端
func (s *BaseService) GetHTTPClient() *resty.Client {
	return s.client.httpClient
}

// GetDefaultHost 获取默认主机地址
func (s *BaseService) GetDefaultHost() string {
	return defaultHost
}

// mapToStruct 将map转换为结构体的通用方法
func (s *BaseService) mapToStruct(data map[string]interface{}, target interface{}) error {
	// 使用JSON序列化/反序列化进行类型转换
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal data: %w", err)
	}
	
	if err := json.Unmarshal(jsonData, target); err != nil {
		return fmt.Errorf("failed to unmarshal to target: %w", err)
	}
	
	return nil
}
