package ozonapi

import (
	"encoding/json"
	"time"
)

// CommonResponse 通用响应结构，参考ozon-client的CommonResponse
type CommonResponse struct {
	// 错误信息
	Error *ErrorResponse `json:"error,omitempty"`
	// 请求ID
	RequestID string `json:"request_id,omitempty"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	// 错误代码
	Code string `json:"code"`
	// 错误消息
	Message string `json:"message"`
	// 错误详情
	Details []ErrorDetail `json:"details,omitempty"`
}

// ErrorDetail 错误详情
type ErrorDetail struct {
	// 类型URL
	TypeURL string `json:"typeUrl,omitempty"`
	// 值
	Value string `json:"value,omitempty"`
}

// Language 语言枚举
type Language string

const (
	LanguageDefault Language = "DEFAULT"
	LanguageEN      Language = "EN"
	LanguageRU      Language = "RU"
)

// Currency 货币枚举
type Currency string

const (
	CurrencyRUB Currency = "RUB"
	CurrencyUSD Currency = "USD"
	CurrencyEUR Currency = "EUR"
	CurrencyBYN Currency = "BYN"
	CurrencyKZT Currency = "KZT"
)

// ProductState 产品状态枚举
type ProductState string

const (
	ProductStateProcessed        ProductState = "processed"
	ProductStateProcessing       ProductState = "processing"
	ProductStateModerating       ProductState = "moderating"
	ProductStateFailedModeration ProductState = "failed_moderation"
	ProductStateFailedValidation ProductState = "failed_validation"
	ProductStateFailed           ProductState = "failed"
	ProductStateArchived         ProductState = "archived"
)

// Visibility 可见性枚举
type Visibility string

const (
	VisibilityAll           Visibility = "ALL"
	VisibilityVisible       Visibility = "VISIBLE"
	VisibilityInvisible     Visibility = "INVISIBLE"
	VisibilityEmptyStock    Visibility = "EMPTY_STOCK"
	VisibilityReadyToSupply Visibility = "READY_TO_SUPPLY"
	VisibilityStateFailed   Visibility = "STATE_FAILED"
)

// PostingStatus 发货单状态枚举
type PostingStatus string

const (
	PostingStatusAwaitingApprove   PostingStatus = "awaiting_approve"
	PostingStatusAwaitingPackaging PostingStatus = "awaiting_packaging"
	PostingStatusAwaitingDeliver   PostingStatus = "awaiting_deliver"
	PostingStatusDelivering        PostingStatus = "delivering"
	PostingStatusDelivered         PostingStatus = "delivered"
	PostingStatusCancelled         PostingStatus = "cancelled"
	PostingStatusNotAccepted       PostingStatus = "not_accepted"
)

// WarehouseType 仓库类型枚举
type WarehouseType string

const (
	WarehouseTypeAll WarehouseType = "ALL"
	WarehouseTypeFBO WarehouseType = "FBO"
	WarehouseTypeFBS WarehouseType = "FBS"
)

// ShipmentType 发货类型枚举
type ShipmentType string

const (
	ShipmentTypeFBO ShipmentType = "fbo"
	ShipmentTypeFBS ShipmentType = "fbs"
)

// DeliveryType 配送类型枚举
type DeliveryType string

const (
	DeliveryTypeCourier    DeliveryType = "courier"
	DeliveryTypePickup     DeliveryType = "pickup"
	DeliveryTypePostOffice DeliveryType = "post_office"
)

// PaymentType 支付类型枚举
type PaymentType string

const (
	PaymentTypeCash       PaymentType = "cash"
	PaymentTypeCard       PaymentType = "card"
	PaymentTypeOnline     PaymentType = "online"
	PaymentTypeInstalment PaymentType = "instalment"
)

// DimensionUnit 尺寸单位枚举
type DimensionUnit string

const (
	DimensionUnitMM DimensionUnit = "mm"
	DimensionUnitCM DimensionUnit = "cm"
	DimensionUnitIN DimensionUnit = "in"
)

// WeightUnit 重量单位枚举
type WeightUnit string

const (
	WeightUnitG  WeightUnit = "g"
	WeightUnitKG WeightUnit = "kg"
	WeightUnitLB WeightUnit = "lb"
)

// SortDirection 排序方向枚举
type SortDirection string

const (
	SortDirectionASC  SortDirection = "ASC"
	SortDirectionDESC SortDirection = "DESC"
)

// TimeFormat 时间格式结构
type TimeFormat struct {
	time.Time
}

// MarshalJSON 自定义JSON序列化
func (t *TimeFormat) MarshalJSON() ([]byte, error) {
	if t.Time.IsZero() {
		return []byte("null"), nil
	}
	return json.Marshal(t.Time.Format("2006-01-02T15:04:05Z"))
}

// UnmarshalJSON 自定义JSON反序列化
func (t *TimeFormat) UnmarshalJSON(data []byte) error {
	var str string
	if err := json.Unmarshal(data, &str); err != nil {
		return err
	}
	
	if str == "" || str == "null" {
		t.Time = time.Time{}
		return nil
	}
	
	// 尝试多种时间格式
	formats := []string{
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		"2006-01-02T15:04:05+00:00",
		"2006-01-02T15:04:05.000+00:00",
		"2006-01-02 15:04:05",
		"2006-01-02",
	}
	
	for _, format := range formats {
		if parsed, err := time.Parse(format, str); err == nil {
			t.Time = parsed
			return nil
		}
	}
	
	return &time.ParseError{
		Layout:     "multiple formats",
		Value:      str,
		LayoutElem: "",
		ValueElem:  str,
		Message:    "cannot parse time",
	}
}

// DateFormat 日期格式结构
type DateFormat struct {
	time.Time
}

// MarshalJSON 自定义JSON序列化
func (d *DateFormat) MarshalJSON() ([]byte, error) {
	if d.Time.IsZero() {
		return []byte("null"), nil
	}
	return json.Marshal(d.Time.Format("2006-01-02"))
}

// UnmarshalJSON 自定义JSON反序列化
func (d *DateFormat) UnmarshalJSON(data []byte) error {
	var str string
	if err := json.Unmarshal(data, &str); err != nil {
		return err
	}
	
	if str == "" || str == "null" {
		d.Time = time.Time{}
		return nil
	}
	
	parsed, err := time.Parse("2006-01-02", str)
	if err != nil {
		return err
	}
	
	d.Time = parsed
	return nil
}

// Money 金额结构
type Money struct {
	// 货币代码
	CurrencyCode Currency `json:"currency_code"`
	// 金额（以最小货币单位表示，例如戈比）
	Units int64 `json:"units"`
	// 小数部分（纳米单位）
	Nanos int32 `json:"nanos"`
}

// ToFloat64 将金额转换为浮点数
func (m *Money) ToFloat64() float64 {
	return float64(m.Units) + float64(m.Nanos)/1e9
}

// FromFloat64 从浮点数设置金额
func (m *Money) FromFloat64(amount float64, currency Currency) {
	m.CurrencyCode = currency
	m.Units = int64(amount)
	m.Nanos = int32((amount - float64(m.Units)) * 1e9)
}

// Dimensions 尺寸结构
type Dimensions struct {
	// 高度
	Height int32 `json:"height"`
	// 长度
	Length int32 `json:"length"`
	// 宽度
	Width int32 `json:"width"`
	// 尺寸单位
	Unit DimensionUnit `json:"unit"`
}

// Weight 重量结构
type Weight struct {
	// 重量值
	Value int32 `json:"value"`
	// 重量单位
	Unit WeightUnit `json:"unit"`
}

// Address 地址结构
type Address struct {
	// 地址行1
	AddressLine1 string `json:"address_line_1"`
	// 地址行2
	AddressLine2 string `json:"address_line_2,omitempty"`
	// 城市
	City string `json:"city"`
	// 国家代码
	CountryCode string `json:"country_code"`
	// 邮政编码
	PostalCode string `json:"postal_code"`
	// 省/州
	Province string `json:"province,omitempty"`
	// 省/州代码
	ProvinceCode string `json:"province_code,omitempty"`
}

// Coordinates 坐标结构
type Coordinates struct {
	// 纬度
	Latitude float64 `json:"latitude"`
	// 经度
	Longitude float64 `json:"longitude"`
}

// Pagination 分页结构
type Pagination struct {
	// 偏移量
	Offset int32 `json:"offset"`
	// 限制数量
	Limit int32 `json:"limit"`
	// 总数
	Total int32 `json:"total"`
	// 是否有下一页
	HasNext bool `json:"has_next"`
}

// Filter 通用过滤器接口
type Filter interface {
	// Validate 验证过滤器参数
	Validate() error
}

// Sorter 通用排序接口
type Sorter interface {
	// GetSortField 获取排序字段
	GetSortField() string
	// GetSortDirection 获取排序方向
	GetSortDirection() SortDirection
}
