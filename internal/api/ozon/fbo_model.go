package ozonapi

import (
	"time"
)

// ClusterListRequest 获取集群和仓库信息请求
type ClusterListRequest struct {
	ClusterIDs  []int64 `json:"cluster_ids,omitempty"`
	ClusterType string  `json:"cluster_type"` // CLUSTER_TYPE_OZON, CLUSTER_TYPE_CIS
}

// ClusterListResponse 获取集群和仓库信息响应
type ClusterListResponse struct {
	Clusters []ClusterInfo `json:"clusters"`
	Id       int           `json:"id"`
	Name     string        `json:"name"`
	Type     string        `json:"type"`
}

type ClusterInfo struct {
	LogisticClusters []LogisticClusters `json:"logistic_clusters"`
	Id               int                `json:"id"`
	Name             string             `json:"name"`
	Type             string             `json:"type"`
}
type LogisticClusters struct {
	Warehouses []struct {
		WarehouseId int64  `json:"warehouse_id"`
		Type        string `json:"type"`
		Name        string `json:"name"`
	} `json:"warehouses"`
}

// WarehouseFboListRequest 搜索供应点请求
type WarehouseFboListRequest struct {
	FilterBySupplyType []string `json:"filter_by_supply_type"` //跨区直送 CREATE_TYPE_CROSSDOCK, 直接 CREATE_TYPE_DIRECT
	Search             string   `json:"search"`
}

// WarehouseFboListResponse 搜索供应点响应
type WarehouseFboListResponse struct {
	Search []struct {
		Address     string `json:"address"`
		Coordinates struct {
			Latitude  float64 `json:"latitude"`
			Longitude float64 `json:"longitude"`
		} `json:"coordinates"`
		Name          string `json:"name"`
		WarehouseID   int64  `json:"warehouse_id"`
		WarehouseType string `json:"warehouse_type"` // WAREHOUSE_TYPE_DELIVERY_POINT, WAREHOUSE_TYPE_ORDERS_RECEIVING_POINT, etc.
	} `json:"search"`
}

// DraftCreateRequest 创建供应草稿请求
type DraftCreateRequest struct {
	ClusterIDs              []int64 `json:"cluster_ids,omitempty"`
	DropOffPointWarehouseID int64   `json:"drop_off_point_warehouse_id,omitempty"`
	Items                   []struct {
		Quantity int    `json:"quantity"`
		SKU      string `json:"sku"`
	} `json:"items"`
	Type string `json:"type"` // CREATE_TYPE_CROSSDOCK, CREATE_TYPE_DIRECT
}

// DraftCreateInfoResponse 创建供应草稿响应
type DraftCreateInfoResponse struct {
	Clusters []DraftCreateInfoCluster `json:"clusters"`
	DraftID  int64                    `json:"draft_id"`
	Errors   []interface{}            `json:"errors"`
	Status   string                   `json:"status"` // CALCULATION_STATUS_FAILED, CALCULATION_STATUS_SUCCESS, etc.
}

// DraftCreateInfoCluster 草稿创建信息中的集群
type DraftCreateInfoCluster struct {
	ClusterID   int64                      `json:"cluster_id"`
	ClusterName string                     `json:"cluster_name"`
	Warehouses  []DraftCreateInfoWarehouse `json:"warehouses"`
}

// DraftCreateInfoWarehouse 草稿创建信息中的仓库
type DraftCreateInfoWarehouse struct {
	SupplyWarehouse struct {
		Name        string `json:"name"`
		WarehouseID int64  `json:"warehouse_id"`
	} `json:"supply_warehouse"`
}

// DraftTimeslotInfoRequest 获取可用时间段请求
type DraftTimeslotInfoRequest struct {
	DateFrom     time.Time `json:"date_from"`
	DateTo       time.Time `json:"date_to"`
	DraftID      int64     `json:"draft_id"`
	WarehouseIDs []int64   `json:"warehouse_ids"`
}

// DraftTimeslotInfoResponse 获取可用时间段响应
type DraftTimeslotInfoResponse struct {
	DropOffWarehouseTimeslots []WarehouseTimeslot `json:"drop_off_warehouse_timeslots"`
	RequestedDateFrom         time.Time           `json:"requested_date_from"`
	RequestedDateTo           time.Time           `json:"requested_date_to"`
}

// WarehouseTimeslot 仓库时间段
type WarehouseTimeslot struct {
	DropOffWarehouseID int64 `json:"drop_off_warehouse_id"`
	Days               []struct {
		DateInTimezone time.Time  `json:"date_in_timezone"`
		Timeslots      []Timeslot `json:"timeslots"`
	} `json:"days"`
}

// Timeslot 时间段
type Timeslot struct {
	FromInTimezone time.Time `json:"from_in_timezone"`
	ToInTimezone   time.Time `json:"to_in_timezone"`
}

// DraftSupplyCreateRequest 从草稿创建供应单请求
type DraftSupplyCreateRequest struct {
	DraftID     int64     `json:"draft_id"`
	Timeslot    *Timeslot `json:"timeslot,omitempty"`
	WarehouseID int64     `json:"warehouse_id"`
}

// DraftSupplyCreateStatusResponse 从草稿创建供应单状态响应
type DraftSupplyCreateStatusResponse struct {
	ErrorMessages []string `json:"error_messages"`
	Result        struct {
		OrderIDs []int64 `json:"order_ids"`
	} `json:"result"`
	Status string `json:"status"` // DraftSupplyCreateStatusUnknown, DraftSupplyCreateStatusSuccess, etc.
}

// CargoesCreateRequest 设置货物单位请求
type CargoesCreateRequest struct {
	Cargoes              []CargoItem `json:"cargoes"`
	DeleteCurrentVersion bool        `json:"delete_current_version,omitempty"`
	SupplyID             int64       `json:"supply_id"`
}

// CargoItem 货物项
type CargoItem struct {
	Key   string `json:"key"`
	Value struct {
		Items []struct {
			Barcode   string    `json:"barcode"`
			ExpiresAt time.Time `json:"expires_at,omitempty"`
			Quant     int       `json:"quant"`
			Quantity  int       `json:"quantity"`
		} `json:"items"`
		Type string `json:"type"` // BOX, PALLET
	} `json:"value"`
}

// CargoesCreateResponse 设置货物单位响应
type CargoesCreateResponse struct {
	OperationID string      `json:"operation_id"`
	Errors      interface{} `json:"errors"` // Complex error object
}

// CargoesCreateInfoResponse 获取货物单位设置状态响应
type CargoesCreateInfoResponse struct {
	Result struct {
		Cargoes []struct {
			Key   string `json:"key"`
			Value struct {
				CargoID int64 `json:"cargo_id"`
			} `json:"value"`
		} `json:"cargoes"`
	} `json:"result"`
	Status string      `json:"status"` // SUCCESS, IN_PROGRESS, FAILED
	Errors interface{} `json:"errors"` // Complex error object
}

// CargoesDeleteRequest 删除货物单位请求
type CargoesDeleteRequest struct {
	CargoIDs []int64 `json:"cargo_ids,omitempty"`
	SupplyID int64   `json:"supply_id"`
}

// CargoesDeleteResponse 删除货物单位响应
type CargoesDeleteResponse struct {
	OperationID string      `json:"operation_id"`
	Errors      interface{} `json:"errors"` // Complex error object
}

// CargoesDeleteStatusResponse 获取货物单位删除状态响应
type CargoesDeleteStatusResponse struct {
	Status string      `json:"status"` // SUCCESS, IN_PROGRESS, ERROR
	Errors interface{} `json:"errors"` // Complex error object
}

// CargoesRulesResponse 获取FBO货物设置检查列表响应
type CargoesRulesResponse struct {
	SupplyCheckLists []struct {
		SupplyID  int64 `json:"supply_id"`
		Satisfied bool  `json:"satisfied"`
	} `json:"supply_check_lists"`
}

// CargoesLabelCreateRequest 生成货物标签请求
type CargoesLabelCreateRequest struct {
	Cargoes []struct {
		CargoID int64 `json:"cargo_id"`
	} `json:"cargoes,omitempty"`
	SupplyID int64 `json:"supply_id"`
}

// CargoesLabelGetResponse 获取标签标识符响应
type CargoesLabelGetResponse struct {
	Result struct {
		FileGUID string `json:"file_guid"`
	} `json:"result"`
	Status string      `json:"status"` // SUCCESS, IN_PROGRESS, FAILED
	Errors interface{} `json:"errors"`
}

// SupplyOrderCancelRequest 取消供应单请求
type SupplyOrderCancelRequest struct {
	OrderID int64 `json:"order_id"`
}

// SupplyOrderCancelStatusResponse 获取供应单取消状态响应
type SupplyOrderCancelStatusResponse struct {
	Status       string   `json:"status"` // SUCCESS, IN_PROGRESS, ERROR
	ErrorReasons []string `json:"error_reasons"`
	Result       struct {
		IsOrderCancelled bool `json:"is_order_cancelled"`
		Supplies         []struct {
			SupplyID          int64 `json:"supply_id"`
			IsSupplyCancelled bool  `json:"is_supply_cancelled"`
		} `json:"supplies"`
	} `json:"result"`
}

// SupplyOrderContentUpdateRequest 更新供应单内容请求
type SupplyOrderContentUpdateRequest struct {
	Items []struct {
		Quant    int   `json:"quant,omitempty"`
		Quantity int   `json:"quantity"`
		SKU      int64 `json:"sku"`
	} `json:"items"`
	OrderID  int64 `json:"order_id"`
	SupplyID int64 `json:"supply_id"`
}

// SupplyOrderContentUpdateResponse 更新供应单内容响应
type SupplyOrderContentUpdateResponse struct {
	OperationID string   `json:"operation_id"`
	Errors      []string `json:"errors"`
}

// SupplyOrderContentUpdateStatusResponse 获取供应单内容更新状态响应
type SupplyOrderContentUpdateStatusResponse struct {
	Status string   `json:"status"` // SUCCESS, IN_PROGRESS, ERROR
	Errors []string `json:"errors"`
}
