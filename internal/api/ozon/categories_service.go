package ozonapi

import (
	"context"
	"fmt"
)

// CategoriesService 分类服务，参考ozon-client的Categories结构
type CategoriesService struct {
	*BaseService
}

// NewCategoriesService 创建分类服务
func NewCategoriesService(baseService *BaseService) *CategoriesService {
	return &CategoriesService{
		BaseService: baseService,
	}
}

// GetProductTreeParams 获取产品分类树参数
type GetProductTreeParams struct {
	// 响应语言
	Language Language `json:"language,omitempty"`
}

// GetProductTreeResponse 获取产品分类树响应
type GetProductTreeResponse struct {
	CommonResponse
	// 分类列表
	Result []GetProductTreeResult `json:"result"`
}

// GetProductTreeResult 产品分类树结果
type GetProductTreeResult struct {
	// 分类标识符
	DescriptionCategoryID int64 `json:"description_category_id"`
	// 分类名称
	CategoryName string `json:"category_name"`
	// 如果不能在该分类中创建产品则为true，如果可以则为false
	Disabled bool `json:"disabled"`
	// 产品类型标识符
	TypeID int64 `json:"type_id"`
	// 产品类型名称
	TypeName string `json:"type_name"`
	// 子分类树
	Children []GetProductTreeResult `json:"children"`
}

// Tree 返回树状视图中的产品分类
//
// 只能在最后一级分类中创建新产品。
// 这意味着您需要将这些特定分类与您网站的分类匹配。
// 我们不会根据用户请求创建新分类。
func (s *CategoriesService) Tree(ctx context.Context, params *GetProductTreeParams) (*GetProductTreeResponse, error) {
	result, err := s.Request("POST", "/v1/description-category/tree", params)
	if err != nil {
		return nil, err
	}

	response := &GetProductTreeResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// GetCategoryAttributesParams 获取分类属性参数
type GetCategoryAttributesParams struct {
	// 分类标识符
	DescriptionCategoryID int64 `json:"description_category_id"`
	// 响应语言
	Language Language `json:"language,omitempty"`
	// 产品类型标识符
	TypeID int64 `json:"type_id"`
}

// GetCategoryAttributesResponse 获取分类属性响应
type GetCategoryAttributesResponse struct {
	CommonResponse
	// 方法结果
	Result []GetCategoryAttributesResult `json:"result"`
}

// GetCategoryAttributesResult 分类属性结果
type GetCategoryAttributesResult struct {
	// 指示字典属性值是否依赖于分类：
	// true — 属性对每个分类都有自己的值集。
	// false — 属性对所有分类都有相同的值集
	CategoryDependent bool `json:"category_dependent"`
	// 特征描述
	Description string `json:"description"`
	// 目录标识符
	DictionaryID int64 `json:"dictionary_id"`
	// 特征组标识符
	GroupID int64 `json:"group_id"`
	// 特征组名称
	GroupName string `json:"group_name"`
	// 属性标识符
	ID int64 `json:"id"`
	// 指示属性是否为必需：
	// true — 必需，
	// false — 可选
	IsRequired bool `json:"is_required"`
	// 属性名称
	Name string `json:"name"`
	// 属性类型
	Type string `json:"type"`
	// 指示属性是否为集合：
	// true — 集合，
	// false — 单个值
	IsCollection bool `json:"is_collection"`
}

// GetCategoryAttributes 获取指定分类和产品类型的属性列表
func (s *CategoriesService) GetCategoryAttributes(ctx context.Context, params *GetCategoryAttributesParams) (*GetCategoryAttributesResponse, error) {
	result, err := s.Request("POST", "/v1/description-category/attribute", params)
	if err != nil {
		return nil, err
	}

	response := &GetCategoryAttributesResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// GetAttributeValuesParams 获取属性值参数
type GetAttributeValuesParams struct {
	// 分类标识符
	CategoryID int64 `json:"category_id"`
	// 属性标识符
	AttributeID int64 `json:"attribute_id"`
	// 响应语言
	Language Language `json:"language,omitempty"`
	// 最后一个值ID
	LastValueID int64 `json:"last_value_id,omitempty"`
	// 限制数量
	Limit int32 `json:"limit,omitempty"`
}

// GetAttributeValuesResponse 获取属性值响应
type GetAttributeValuesResponse struct {
	CommonResponse
	// 属性值结果
	Result []GetAttributeValuesResult `json:"result"`
	// 是否有更多数据
	HasNext bool `json:"has_next"`
}

// GetAttributeValuesResult 属性值结果
type GetAttributeValuesResult struct {
	// 值标识符
	ID int64 `json:"id"`
	// 值
	Value string `json:"value"`
	// 信息
	Info string `json:"info"`
	// 图片URL
	Picture string `json:"picture"`
}

// GetAttributeValues 获取指定属性的值列表
func (s *CategoriesService) GetAttributeValues(ctx context.Context, params *GetAttributeValuesParams) (*GetAttributeValuesResponse, error) {
	result, err := s.Request("POST", "/v1/description-category/attribute/values", params)
	if err != nil {
		return nil, err
	}

	response := &GetAttributeValuesResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// SearchAttributeValuesParams 搜索属性值参数
type SearchAttributeValuesParams struct {
	// 分类标识符
	CategoryID int64 `json:"category_id"`
	// 属性标识符
	AttributeID int64 `json:"attribute_id"`
	// 搜索文本
	Search string `json:"search"`
	// 响应语言
	Language Language `json:"language,omitempty"`
	// 限制数量
	Limit int32 `json:"limit,omitempty"`
}

// SearchAttributeValuesResponse 搜索属性值响应
type SearchAttributeValuesResponse struct {
	CommonResponse
	// 搜索结果
	Result []GetAttributeValuesResult `json:"result"`
}

// SearchAttributeValues 搜索指定属性的值
func (s *CategoriesService) SearchAttributeValues(ctx context.Context, params *SearchAttributeValuesParams) (*SearchAttributeValuesResponse, error) {
	result, err := s.Request("POST", "/v1/description-category/attribute/values/search", params)
	if err != nil {
		return nil, err
	}

	response := &SearchAttributeValuesResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// mapToStruct 将map转换为结构体的辅助方法
func (s *CategoriesService) mapToStruct(data map[string]interface{}, target interface{}) error {
	// 这里可以使用json.Marshal/Unmarshal或者其他映射方法
	// 为了简化，这里返回nil，实际使用时需要实现具体的映射逻辑
	return nil
}
