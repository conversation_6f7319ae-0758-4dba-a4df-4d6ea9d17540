package ozonapi

import (
	"fmt"

	"github.com/go-resty/resty/v2"
)

const (
	// API endpoints
	clusterListEndpoint                    = "/v1/cluster/list"
	warehouseFboListEndpoint               = "/v1/warehouse/fbo/list"
	draftCreateEndpoint                    = "/v1/draft/create"
	draftCreateInfoEndpoint                = "/v1/draft/create/info"
	draftTimeslotInfoEndpoint              = "/v1/draft/timeslot/info"
	draftSupplyCreateEndpoint              = "/v1/draft/supply/create"
	draftSupplyCreateStatusEndpoint        = "/v1/draft/supply/create/status"
	cargoesCreateEndpoint                  = "/v1/cargoes/create"
	cargoesCreateInfoEndpoint              = "/v1/cargoes/create/info"
	cargoesDeleteEndpoint                  = "/v1/cargoes/delete"
	cargoesDeleteStatusEndpoint            = "/v1/cargoes/delete/status"
	cargoesRulesGetEndpoint                = "/v1/cargoes/rules/get"
	cargoesLabelCreateEndpoint             = "/v1/cargoes-label/create"
	cargoesLabelGetEndpoint                = "/v1/cargoes-label/get"
	supplyOrderCancelEndpoint              = "/v1/supply-order/cancel"
	supplyOrderCancelStatusEndpoint        = "/v1/supply-order/cancel/status"
	supplyOrderContentUpdateEndpoint       = "/v1/supply-order/content/update"
	supplyOrderContentUpdateStatusEndpoint = "/v1/supply-order/content/update/status"
)

// FBOService FBO服务
type FBOService struct {
	*BaseService
}

// newFBOService 创建新的FBO服务
func newFBOService(baseService *BaseService) *FBOService {
	return &FBOService{
		BaseService: baseService,
	}
}

// GetClusterAndWarehouseList 获取集群和仓库信息
func (s *FBOService) GetClusterAndWarehouseList(req *ClusterListRequest) (*ClusterListResponse, error) {
	resp := &ClusterListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(clusterListEndpoint)
	fmt.Println(httpResp.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get cluster and warehouse list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cluster and warehouse list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// SearchFboWarehouses 搜索FBO仓库
func (s *FBOService) SearchFboWarehouses(req *WarehouseFboListRequest) (*WarehouseFboListResponse, error) {
	resp := &WarehouseFboListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(warehouseFboListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to search FBO warehouses: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("search FBO warehouses failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateSupplyDraft 创建供应草稿
func (s *FBOService) CreateSupplyDraft(req *DraftCreateRequest) (string, error) {
	resp := make(map[string]interface{})
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(&resp).
		Post(draftCreateEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to create supply draft: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("create supply draft failed with status code: %d", httpResp.StatusCode())
	}

	operationID, ok := resp["operation_id"].(string)
	if !ok {
		return "", fmt.Errorf("failed to get operation_id from response")
	}

	return operationID, nil
}

// GetSupplyDraftInfo 获取供应草稿信息
func (s *FBOService) GetSupplyDraftInfo(operationID string) (*DraftCreateInfoResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &DraftCreateInfoResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(draftCreateInfoEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supply draft info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supply draft info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetDraftTimeslotInfo 获取草稿时间段信息
func (s *FBOService) GetDraftTimeslotInfo(req *DraftTimeslotInfoRequest) (*DraftTimeslotInfoResponse, error) {
	resp := &DraftTimeslotInfoResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(draftTimeslotInfoEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get draft timeslot info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get draft timeslot info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateSupplyFromDraft 从草稿创建供应单
func (s *FBOService) CreateSupplyFromDraft(req *DraftSupplyCreateRequest) (string, error) {
	resp := make(map[string]interface{})
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(&resp).
		Post(draftSupplyCreateEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to create supply from draft: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("create supply from draft failed with status code: %d", httpResp.StatusCode())
	}

	operationID, ok := resp["operation_id"].(string)
	if !ok {
		return "", fmt.Errorf("failed to get operation_id from response")
	}

	return operationID, nil
}

// GetDraftSupplyCreateStatus 获取草稿供应单创建状态
func (s *FBOService) GetDraftSupplyCreateStatus(operationID string) (*DraftSupplyCreateStatusResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &DraftSupplyCreateStatusResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(draftSupplyCreateStatusEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get draft supply create status: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get draft supply create status failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateOrUpdateCargoes 创建或更新货物
func (s *FBOService) CreateOrUpdateCargoes(req *CargoesCreateRequest) (string, error) {
	resp := &CargoesCreateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(cargoesCreateEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to create or update cargoes: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("create or update cargoes failed with status code: %d", httpResp.StatusCode())
	}

	return resp.OperationID, nil
}

// GetCargoesCreateInfo 获取货物创建信息
func (s *FBOService) GetCargoesCreateInfo(operationID string) (*CargoesCreateInfoResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &CargoesCreateInfoResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(cargoesCreateInfoEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get cargoes create info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cargoes create info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// DeleteCargoes 删除货物
func (s *FBOService) DeleteCargoes(req *CargoesDeleteRequest) (string, error) {
	resp := &CargoesDeleteResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(cargoesDeleteEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to delete cargoes: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("delete cargoes failed with status code: %d", httpResp.StatusCode())
	}

	return resp.OperationID, nil
}

// GetCargoesDeleteStatus 获取货物删除状态
func (s *FBOService) GetCargoesDeleteStatus(operationID string) (*CargoesDeleteStatusResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &CargoesDeleteStatusResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(cargoesDeleteStatusEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get cargoes delete status: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cargoes delete status failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetCargoesRules 获取货物规则
func (s *FBOService) GetCargoesRules(supplyIDs []int64) (*CargoesRulesResponse, error) {
	req := map[string]interface{}{
		"supply_ids": supplyIDs,
	}
	resp := &CargoesRulesResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(cargoesRulesGetEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get cargoes rules: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cargoes rules failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateCargoesLabel 创建货物标签
func (s *FBOService) CreateCargoesLabel(req *CargoesLabelCreateRequest) (string, error) {
	resp := make(map[string]interface{})
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(&resp).
		Post(cargoesLabelCreateEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to create cargoes label: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("create cargoes label failed with status code: %d", httpResp.StatusCode())
	}

	operationID, ok := resp["operation_id"].(string)
	if !ok {
		return "", fmt.Errorf("failed to get operation_id from response")
	}

	return operationID, nil
}

// GetCargoesLabelInfo 获取货物标签信息
func (s *FBOService) GetCargoesLabelInfo(operationID string) (*CargoesLabelGetResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &CargoesLabelGetResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(cargoesLabelGetEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get cargoes label info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cargoes label info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CancelSupplyOrder 取消供应单
func (s *FBOService) CancelSupplyOrder(req *SupplyOrderCancelRequest) (string, error) {
	resp := make(map[string]interface{})
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(&resp).
		Post(supplyOrderCancelEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to cancel supply order: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("cancel supply order failed with status code: %d", httpResp.StatusCode())
	}

	operationID, ok := resp["operation_id"].(string)
	if !ok {
		return "", fmt.Errorf("failed to get operation_id from response")
	}

	return operationID, nil
}

// GetSupplyOrderCancelStatus 获取供应单取消状态
func (s *FBOService) GetSupplyOrderCancelStatus(operationID string) (*SupplyOrderCancelStatusResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &SupplyOrderCancelStatusResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderCancelStatusEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supply order cancel status: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supply order cancel status failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// UpdateSupplyOrderContent 更新供应单内容
func (s *FBOService) UpdateSupplyOrderContent(req *SupplyOrderContentUpdateRequest) (string, error) {
	resp := &SupplyOrderContentUpdateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderContentUpdateEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to update supply order content: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("update supply order content failed with status code: %d", httpResp.StatusCode())
	}

	return resp.OperationID, nil
}

// GetSupplyOrderContentUpdateStatus 获取供应单内容更新状态
func (s *FBOService) GetSupplyOrderContentUpdateStatus(operationID string) (*SupplyOrderContentUpdateStatusResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &SupplyOrderContentUpdateStatusResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderContentUpdateStatusEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supply order content update status: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supply order content update status failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}
