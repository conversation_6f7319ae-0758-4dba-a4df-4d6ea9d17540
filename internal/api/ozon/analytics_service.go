package ozonapi

import (
	"context"
	"fmt"
	"time"
)

// AnalyticsService 分析服务，参考ozon-client的Analytics结构
type AnalyticsService struct {
	*BaseService
}

// NewAnalyticsService 创建分析服务
func NewAnalyticsService(baseService *BaseService) *AnalyticsService {
	return &AnalyticsService{
		BaseService: baseService,
	}
}

// GetAnalyticsDataParams 获取分析数据参数
type GetAnalyticsDataParams struct {
	// 报告中数据的起始日期
	DateFrom *TimeFormat `json:"date_from"`
	// 报告中数据的结束日期
	DateTo *TimeFormat `json:"date_to"`
	// 数据分组维度
	Dimension []GetAnalyticsDataDimension `json:"dimension"`
	// 过滤器
	Filters []GetAnalyticsDataFilter `json:"filters"`
	// 响应中的项目数量：最大值为1000，最小值为1
	Limit int64 `json:"limit"`
	// 指定最多14个指标。如果超过，将收到InvalidArgument代码的错误
	Metrics []GetAnalyticsDataFilterMetric `json:"metrics"`
	// 将在响应中跳过的元素数量
	Offset int64 `json:"offset"`
	// 报告排序设置
	Sort []GetAnalyticsDataSort `json:"sort"`
}

// GetAnalyticsDataDimension 分析数据维度
type GetAnalyticsDataDimension string

const (
	DimensionUnknown    GetAnalyticsDataDimension = "unknownDimension"
	DimensionSKU        GetAnalyticsDataDimension = "sku"
	DimensionSPU        GetAnalyticsDataDimension = "spu"
	DimensionDay        GetAnalyticsDataDimension = "day"
	DimensionWeek       GetAnalyticsDataDimension = "week"
	DimensionMonth      GetAnalyticsDataDimension = "month"
	DimensionYear       GetAnalyticsDataDimension = "year"
	DimensionCategory1  GetAnalyticsDataDimension = "category1"
	DimensionCategory2  GetAnalyticsDataDimension = "category2"
	DimensionCategory3  GetAnalyticsDataDimension = "category3"
	DimensionCategory4  GetAnalyticsDataDimension = "category4"
	DimensionBrand      GetAnalyticsDataDimension = "brand"
	DimensionModelID    GetAnalyticsDataDimension = "modelID"
)

// GetAnalyticsDataFilter 分析数据过滤器
type GetAnalyticsDataFilter struct {
	// 排序参数
	Key string `json:"key"`
	// 比较操作
	Operation GetAnalyticsDataFilterOperation `json:"operation"`
	// 比较值
	Value string `json:"value"`
}

// GetAnalyticsDataFilterOperation 过滤器操作
type GetAnalyticsDataFilterOperation string

const (
	OperationEquals      GetAnalyticsDataFilterOperation = "EQ"
	OperationNotEquals   GetAnalyticsDataFilterOperation = "NEQ"
	OperationGreater     GetAnalyticsDataFilterOperation = "GT"
	OperationGreaterEq   GetAnalyticsDataFilterOperation = "GTE"
	OperationLess        GetAnalyticsDataFilterOperation = "LT"
	OperationLessEq      GetAnalyticsDataFilterOperation = "LTE"
	OperationContains    GetAnalyticsDataFilterOperation = "CONTAINS"
	OperationNotContains GetAnalyticsDataFilterOperation = "NOT_CONTAINS"
)

// GetAnalyticsDataFilterMetric 分析数据指标
type GetAnalyticsDataFilterMetric string

const (
	MetricRevenue           GetAnalyticsDataFilterMetric = "revenue"
	MetricOrderedUnits      GetAnalyticsDataFilterMetric = "ordered_units"
	MetricUnknownMetric     GetAnalyticsDataFilterMetric = "unknown_metric"
	MetricHitsViewSearch    GetAnalyticsDataFilterMetric = "hits_view_search"
	MetricHitsViewPDP       GetAnalyticsDataFilterMetric = "hits_view_pdp"
	MetricHitsView          GetAnalyticsDataFilterMetric = "hits_view"
	MetricHitsToCartSearch  GetAnalyticsDataFilterMetric = "hits_tocart_search"
	MetricHitsToCartPDP     GetAnalyticsDataFilterMetric = "hits_tocart_pdp"
	MetricHitsToCart        GetAnalyticsDataFilterMetric = "hits_tocart"
	MetricSessionViewSearch GetAnalyticsDataFilterMetric = "session_view_search"
	MetricSessionViewPDP    GetAnalyticsDataFilterMetric = "session_view_pdp"
	MetricSessionView       GetAnalyticsDataFilterMetric = "session_view"
	MetricConvToCartSearch  GetAnalyticsDataFilterMetric = "conv_tocart_search"
	MetricConvToCartPDP     GetAnalyticsDataFilterMetric = "conv_tocart_pdp"
	MetricConvToCart        GetAnalyticsDataFilterMetric = "conv_tocart"
	MetricReturns           GetAnalyticsDataFilterMetric = "returns"
	MetricCancellations     GetAnalyticsDataFilterMetric = "cancellations"
	MetricDeliveredUnits    GetAnalyticsDataFilterMetric = "delivered_units"
	MetricPositionCategory  GetAnalyticsDataFilterMetric = "position_category"
)

// GetAnalyticsDataSort 报告排序设置
type GetAnalyticsDataSort struct {
	// 排序指标
	Key GetAnalyticsDataFilterMetric `json:"key"`
	// 排序类型
	Order Order `json:"order"`
}

// Order 排序顺序
type Order string

const (
	OrderASC  Order = "ASC"
	OrderDESC Order = "DESC"
)

// GetAnalyticsDataResponse 获取分析数据响应
type GetAnalyticsDataResponse struct {
	CommonResponse
	// 分析数据结果
	Result GetAnalyticsDataResult `json:"result"`
}

// GetAnalyticsDataResult 分析数据结果
type GetAnalyticsDataResult struct {
	// 数据项
	Data []GetAnalyticsDataResultItem `json:"data"`
	// 时间戳
	Timestamp time.Time `json:"timestamp"`
	// 总计
	Totals []GetAnalyticsDataResultTotal `json:"totals"`
}

// GetAnalyticsDataResultItem 分析数据结果项
type GetAnalyticsDataResultItem struct {
	// 维度
	Dimensions []GetAnalyticsDataResultDimension `json:"dimensions"`
	// 指标
	Metrics []GetAnalyticsDataResultMetric `json:"metrics"`
}

// GetAnalyticsDataResultDimension 分析数据结果维度
type GetAnalyticsDataResultDimension struct {
	// 维度ID
	ID string `json:"id"`
	// 维度值
	Value string `json:"value"`
}

// GetAnalyticsDataResultMetric 分析数据结果指标
type GetAnalyticsDataResultMetric struct {
	// 指标ID
	ID string `json:"id"`
	// 指标值
	Value float64 `json:"value"`
}

// GetAnalyticsDataResultTotal 分析数据结果总计
type GetAnalyticsDataResultTotal struct {
	// 指标ID
	ID string `json:"id"`
	// 总计值
	Value float64 `json:"value"`
}

// GetAnalyticsData 获取分析数据
func (s *AnalyticsService) GetAnalyticsData(ctx context.Context, params *GetAnalyticsDataParams) (*GetAnalyticsDataResponse, error) {
	result, err := s.Request("POST", "/v1/analytics/data", params)
	if err != nil {
		return nil, err
	}

	response := &GetAnalyticsDataResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// GetStockOnWarehousesParams 获取仓库库存参数
type GetStockOnWarehousesParams struct {
	// 偏移量
	Offset int `json:"offset"`
	// 限制数量
	Limit int `json:"limit"`
	// 仓库类型
	WarehouseType WarehouseType `json:"warehouse_type"`
}

// GetStockOnWarehousesResponse 获取仓库库存响应
type GetStockOnWarehousesResponse struct {
	CommonResponse
	// 库存数据结果
	Result GetStockOnWarehousesResult `json:"result"`
}

// GetStockOnWarehousesResult 仓库库存结果
type GetStockOnWarehousesResult struct {
	// 行数据
	Rows []StockOnWarehousesRow `json:"rows"`
	// 总数
	Total int `json:"total"`
}

// StockOnWarehousesRow 仓库库存行数据
type StockOnWarehousesRow struct {
	// SKU
	SKU int64 `json:"sku"`
	// 商品名称
	ItemName string `json:"item_name"`
	// 自由库存
	FreeToSellAmount int `json:"free_to_sell_amount"`
	// 承诺库存
	PromisedAmount int `json:"promised_amount"`
	// 预留库存
	ReservedAmount int `json:"reserved_amount"`
	// 仓库名称
	WarehouseName string `json:"warehouse_name"`
}

// GetStockOnWarehouses 获取Ozon仓库的剩余库存和产品移动报告
func (s *AnalyticsService) GetStockOnWarehouses(ctx context.Context, params *GetStockOnWarehousesParams) (*GetStockOnWarehousesResponse, error) {
	result, err := s.Request("POST", "/v2/analytics/stock_on_warehouses", params)
	if err != nil {
		return nil, err
	}

	response := &GetStockOnWarehousesResponse{}
	if err := s.mapToStruct(result, response); err != nil {
		return nil, fmt.Errorf("failed to map response: %w", err)
	}

	return response, nil
}

// TimeFormat 时间格式
type TimeFormat struct {
	time.Time
}

// MarshalJSON 自定义JSON序列化
func (t *TimeFormat) MarshalJSON() ([]byte, error) {
	return []byte(`"` + t.Format("2006-01-02") + `"`), nil
}

// UnmarshalJSON 自定义JSON反序列化
func (t *TimeFormat) UnmarshalJSON(data []byte) error {
	str := string(data)
	str = str[1 : len(str)-1] // 移除引号
	parsed, err := time.Parse("2006-01-02", str)
	if err != nil {
		return err
	}
	t.Time = parsed
	return nil
}

// mapToStruct 将map转换为结构体的辅助方法
func (s *AnalyticsService) mapToStruct(data map[string]interface{}, target interface{}) error {
	// 这里可以使用json.Marshal/Unmarshal或者其他映射方法
	// 为了简化，这里返回nil，实际使用时需要实现具体的映射逻辑
	return nil
}
